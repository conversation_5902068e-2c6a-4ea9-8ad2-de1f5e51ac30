# ESP32 在 Apple Silicon Mac 上的架构兼容性解决方案

## 问题描述
在 Apple Silicon (ARM64) Mac 上编译 ESP32 项目时，遇到 libclang 架构不兼容问题：
- 错误：`have 'x86_64', need 'arm64e' or 'arm64'`
- 原因：ESP-IDF 工具链中的某些组件仍然是 x86_64 架构

## 解决方案

### 方案 1：使用 Rosetta 2 运行编译命令

```bash
# 使用 Rosetta 2 运行 cargo 命令
arch -x86_64 cargo build --release --bin esp32-chat-bot-sim

# 或者创建别名（添加到 ~/.zshrc）
alias cargo-x86="arch -x86_64 cargo"
```

### 方案 2：设置 libclang 路径环境变量

```bash
# 使用系统的 libclang（如果已安装 Xcode）
export LIBCLANG_PATH="/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib"

# 或者使用 Homebrew 安装的 libclang
brew install llvm
export LIBCLANG_PATH="/opt/homebrew/lib"
```

### 方案 3：使用 Docker 容器编译

```bash
# 创建 Dockerfile（见下方）
docker build -t esp32-rust .
docker run --rm -v $(pwd):/workspace esp32-rust cargo build --release
```

### 方案 4：更新工具链版本

```bash
# 清理现有工具链
rm -rf ~/.embuild/
rm -rf ~/.espressif/

# 重新安装最新版本
cargo install cargo-espflash espmonitor
cargo install espup
espup install
```

## 推荐的工作流程

### 临时解决方案（立即可用）
```bash
# 使用 Rosetta 2 编译
./run.sh build-x86 esp32-chat-bot-sim

# 然后在 QEMU 中运行（原生 ARM64）
./run.sh qemu esp32-chat-bot-sim
```

### 长期解决方案
1. 等待 Espressif 发布 ARM64 原生工具链
2. 使用 Docker 容器进行编译
3. 使用持续集成服务进行编译

## 注意事项

- QEMU 本身运行正常（我们安装的是 ARM64 版本）
- 只有编译过程受到影响
- 一旦编译成功，生成的二进制文件可以正常在 QEMU 中运行