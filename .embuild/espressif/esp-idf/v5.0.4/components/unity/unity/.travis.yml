language: c

matrix:
 include:
    - os: osx
      compiler: clang
      osx_image: xcode7.3
    - os: linux
      dist: trusty
      compiler: gcc

before_install:
  - if [ "$TRAVIS_OS_NAME" == "osx" ]; then rvm install 2.1 && rvm use 2.1 && ruby -v; fi
  - if [ "$TRAVIS_OS_NAME" == "linux" ]; then sudo apt-get install --assume-yes --quiet gcc-multilib; fi
install:
  - gem install rspec
  - gem install rubocop -v 0.57.2
script:
  - cd test && rake ci
  - make -s
  - make -s DEBUG=-m32 #32-bit architecture with 64-bit support
  - make -s DEBUG=-m32 UNITY_SUPPORT_64= #32-bit build without 64-bit types
  - make -s UNITY_INCLUDE_DOUBLE= # without double
  - cd ../extras/fixture/test && rake ci
  - make -s default noStdlibMalloc
  - make -s C89
  - cd ../../../examples/example_1 && make -s ci
  - cd ../example_2 && make -s ci
  - cd ../example_3 && rake
