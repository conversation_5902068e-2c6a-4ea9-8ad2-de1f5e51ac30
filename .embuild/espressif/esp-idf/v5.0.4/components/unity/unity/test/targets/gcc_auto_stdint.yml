compiler:
  path: gcc
  source_path:     '../src/'
  unit_tests_path: &unit_tests_path 'tests/'
  build_path:      &build_path 'build/'
  options:
    - '-c'
    - '-m64'
    - '-Wall'
    - '-Wno-address'
    - '-std=c99'
    - '-pedantic'
    - '-Wextra'
    - '-Werror'
    - '-Wpointer-arith'
    - '-Wcast-align'
    - '-Wwrite-strings'
    - '-Wswitch-default'
    - '-Wunreachable-code'
    - '-Winit-self'
    - '-Wmissing-field-initializers'
    - '-Wno-unknown-pragmas'
    - '-Wstrict-prototypes'
    - '-Wundef'
    - '-Wold-style-definition'
  includes:
    prefix: '-I'
    items:
      - 'src/'
      - '../src/'
      - 'testdata/'
      - *unit_tests_path
  defines:
    prefix: '-D'
    items:
      - UNITY_INCLUDE_DOUBLE
      - UNITY_SUPPORT_TEST_CASES
      - UNITY_SUPPORT_64
  object_files:
    prefix: '-o'
    extension: '.o'
    destination: *build_path
linker:
  path: gcc
  options:
    - -lm
    - '-m64'
  includes:
    prefix: '-I'
  object_files:
    path: *build_path
    extension: '.o'
  bin_files:
    prefix: '-o'
    extension: '.exe'
    destination: *build_path
colour: true
:unity:
  :plugins: []
