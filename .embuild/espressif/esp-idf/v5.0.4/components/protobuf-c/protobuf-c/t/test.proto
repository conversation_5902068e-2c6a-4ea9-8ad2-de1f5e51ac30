package foo;

import "protobuf-c/protobuf-c.proto";

option (pb_c_file).c_package = "foo";

message Person {
  required string name = 1;
  required int32 id = 2;
  optional string email = 3;

  enum PhoneType {
    MOBILE = 0;
    HOME = 1;
    WORK = 2;
  }

  message PhoneNumber {
    message Comment {
      required string comment = 1;
    }

    required string number = 1;
    optional PhoneType type = 2 [default = HOME];
    optional Comment comment = 3;
  }

  repeated PhoneNumber phone = 4;
}

message LookupResult
{
  optional Person person = 1;
}

message Name {
  optional string name = 1;
};

service DirLookup {
  rpc ByName (Name) returns (LookupResult);
}
