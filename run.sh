#!/bin/bash

# ESP32 项目运行脚本
# 使用方法: ./run.sh [qemu|flash|build] [binary_name]

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 默认二进制文件
DEFAULT_HARDWARE_BIN="esp32-chat-bot"
DEFAULT_SIM_BIN="esp32-chat-bot-sim"

# 获取二进制文件名参数
BINARY_NAME="${2:-}"

case "${1:-help}" in
    "qemu"|"sim"|"emulator")
        # 如果没有指定二进制文件，默认使用模拟器版本
        if [ -z "$BINARY_NAME" ]; then
            BINARY_NAME="$DEFAULT_SIM_BIN"
        fi
        echo "🚀 在 QEMU 模拟器中运行 $BINARY_NAME..."
        cargo build --release --bin "$BINARY_NAME"
        if [ ! -f "./target/xtensa-esp32-espidf/release/$BINARY_NAME" ]; then
            echo "❌ 编译失败，未找到可执行文件: $BINARY_NAME"
            exit 1
        fi
        echo "✅ 编译完成，启动 QEMU..."
        exec qemu-system-xtensa-esp32 \
            -nographic \
            -M esp32 \
            -m 4M \
            -kernel "./target/xtensa-esp32-espidf/release/$BINARY_NAME"
        ;;
    
    "flash"|"hardware"|"hw")
        # 如果没有指定二进制文件，默认使用硬件版本
        if [ -z "$BINARY_NAME" ]; then
            BINARY_NAME="$DEFAULT_HARDWARE_BIN"
        fi
        echo "📱 刷写 $BINARY_NAME 到硬件设备..."
        cargo run --release --bin "$BINARY_NAME"
        ;;
    
    "build"|"compile")
        if [ -z "$BINARY_NAME" ]; then
            echo "🔨 编译所有二进制文件..."
            cargo build --release
        else
            echo "🔨 编译 $BINARY_NAME..."
            cargo build --release --bin "$BINARY_NAME"
        fi
        ;;
    
    "build-x86"|"compile-x86")
        echo "🔨 使用 Rosetta 2 编译 (x86_64)..."
        if [ -z "$BINARY_NAME" ]; then
            arch -x86_64 cargo build --release
        else
            arch -x86_64 cargo build --release --bin "$BINARY_NAME"
        fi
        ;;
    
    "clean")
        echo "🧹 清理构建文件..."
        cargo clean
        ;;
    
    "check")
        echo "🔍 检查代码..."
        cargo check
        ;;
    
    "list"|"bins")
        echo "📋 可用的二进制文件:"
        echo "  esp32-chat-bot      - 硬件版本"
        echo "  esp32-chat-bot-sim  - 模拟器版本"
        ;;
    
    "help"|*)
        echo "ESP32 Rust 项目运行脚本"
        echo ""
        echo "用法: $0 [命令] [二进制文件名]"
        echo ""
        echo "命令:"
        echo "  qemu, sim, emulator  - 在 QEMU 模拟器中运行 (默认: esp32-chat-bot-sim)"
        echo "  flash, hardware, hw  - 刷写到真实硬件 (默认: esp32-chat-bot)"
        echo "  build, compile       - 编译项目"
        echo "  build-x86, compile-x86 - 使用 Rosetta 2 编译 (解决架构问题)"
        echo "  clean               - 清理构建文件"
        echo "  check               - 检查代码语法"
        echo "  list, bins          - 列出可用的二进制文件"
        echo "  help                - 显示此帮助信息"
        echo ""
        echo "二进制文件:"
        echo "  esp32-chat-bot      - 硬件版本"
        echo "  esp32-chat-bot-sim  - 模拟器版本"
        echo ""
        echo "示例:"
        echo "  $0 qemu                        # 在模拟器中运行 esp32-chat-bot-sim"
        echo "  $0 flash                       # 刷写 esp32-chat-bot 到硬件"
        echo "  $0 qemu esp32-chat-bot         # 在模拟器中运行硬件版本"
        echo "  $0 build esp32-chat-bot-sim    # 仅编译模拟器版本"
        ;;
esac