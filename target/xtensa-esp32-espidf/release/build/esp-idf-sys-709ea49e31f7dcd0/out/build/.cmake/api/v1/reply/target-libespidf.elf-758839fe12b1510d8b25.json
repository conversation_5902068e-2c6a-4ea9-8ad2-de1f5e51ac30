{"artifacts": [{"path": "libespidf.elf"}], "backtrace": 2, "backtraceGraph": {"commands": ["add_executable", "project", "target_link_libraries", "set_property", "idf_build_executable", "add_subdirectory", "target_linker_script", "include", "rom_linker_script", "add_dependencies", "__ldgen_process_template"], "files": ["/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/tools/cmake/project.cmake", "CMakeLists.txt", "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/tools/cmake/build.cmake", "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/mbedtls/CMakeLists.txt", "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_wifi/CMakeLists.txt", "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/CMakeLists.txt", "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_app_format/CMakeLists.txt", "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/pthread/CMakeLists.txt", "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_system/CMakeLists.txt", "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/tools/cmake/utilities.cmake", "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_system/ld/ld.cmake", "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/CMakeLists.txt", "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/CMakeLists.txt", "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/CMakeLists.txt", "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/freertos/CMakeLists.txt", "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/CMakeLists.txt", "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/cxx/CMakeLists.txt", "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_phy/CMakeLists.txt", "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/vfs/CMakeLists.txt", "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/tools/cmake/ldgen.cmake"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 28, "parent": 0}, {"command": 0, "file": 0, "line": 576, "parent": 1}, {"command": 2, "file": 0, "line": 608, "parent": 1}, {"command": 2, "file": 0, "line": 608, "parent": 1}, {"command": 2, "file": 0, "line": 608, "parent": 1}, {"command": 2, "file": 0, "line": 608, "parent": 1}, {"command": 2, "file": 0, "line": 608, "parent": 1}, {"command": 2, "file": 0, "line": 608, "parent": 1}, {"command": 2, "file": 0, "line": 608, "parent": 1}, {"command": 2, "file": 0, "line": 608, "parent": 1}, {"command": 2, "file": 0, "line": 608, "parent": 1}, {"command": 2, "file": 0, "line": 608, "parent": 1}, {"command": 2, "file": 0, "line": 608, "parent": 1}, {"command": 2, "file": 0, "line": 608, "parent": 1}, {"command": 2, "file": 0, "line": 608, "parent": 1}, {"command": 2, "file": 0, "line": 608, "parent": 1}, {"command": 2, "file": 0, "line": 608, "parent": 1}, {"command": 2, "file": 0, "line": 608, "parent": 1}, {"command": 2, "file": 0, "line": 608, "parent": 1}, {"command": 2, "file": 0, "line": 608, "parent": 1}, {"command": 2, "file": 0, "line": 608, "parent": 1}, {"command": 2, "file": 0, "line": 608, "parent": 1}, {"command": 2, "file": 0, "line": 608, "parent": 1}, {"command": 2, "file": 0, "line": 608, "parent": 1}, {"command": 2, "file": 0, "line": 608, "parent": 1}, {"command": 2, "file": 0, "line": 608, "parent": 1}, {"command": 2, "file": 0, "line": 608, "parent": 1}, {"command": 2, "file": 0, "line": 608, "parent": 1}, {"command": 2, "file": 0, "line": 608, "parent": 1}, {"command": 2, "file": 0, "line": 608, "parent": 1}, {"command": 2, "file": 0, "line": 608, "parent": 1}, {"command": 2, "file": 0, "line": 608, "parent": 1}, {"command": 2, "file": 0, "line": 608, "parent": 1}, {"command": 2, "file": 0, "line": 608, "parent": 1}, {"command": 2, "file": 0, "line": 608, "parent": 1}, {"command": 2, "file": 0, "line": 608, "parent": 1}, {"command": 2, "file": 0, "line": 608, "parent": 1}, {"command": 2, "file": 0, "line": 608, "parent": 1}, {"command": 2, "file": 0, "line": 608, "parent": 1}, {"command": 2, "file": 0, "line": 608, "parent": 1}, {"command": 2, "file": 0, "line": 608, "parent": 1}, {"command": 2, "file": 0, "line": 608, "parent": 1}, {"command": 2, "file": 0, "line": 608, "parent": 1}, {"command": 2, "file": 0, "line": 608, "parent": 1}, {"command": 2, "file": 0, "line": 608, "parent": 1}, {"command": 2, "file": 0, "line": 608, "parent": 1}, {"command": 2, "file": 0, "line": 608, "parent": 1}, {"command": 2, "file": 0, "line": 608, "parent": 1}, {"command": 2, "file": 0, "line": 608, "parent": 1}, {"command": 2, "file": 0, "line": 608, "parent": 1}, {"command": 2, "file": 0, "line": 608, "parent": 1}, {"command": 2, "file": 0, "line": 608, "parent": 1}, {"command": 2, "file": 0, "line": 608, "parent": 1}, {"command": 2, "file": 0, "line": 608, "parent": 1}, {"command": 2, "file": 0, "line": 608, "parent": 1}, {"command": 2, "file": 0, "line": 608, "parent": 1}, {"command": 2, "file": 0, "line": 608, "parent": 1}, {"command": 2, "file": 0, "line": 608, "parent": 1}, {"command": 2, "file": 0, "line": 608, "parent": 1}, {"command": 2, "file": 0, "line": 608, "parent": 1}, {"command": 2, "file": 0, "line": 608, "parent": 1}, {"command": 2, "file": 0, "line": 608, "parent": 1}, {"command": 2, "file": 0, "line": 608, "parent": 1}, {"command": 2, "file": 0, "line": 617, "parent": 1}, {"command": 4, "file": 0, "line": 656, "parent": 1}, {"command": 3, "file": 2, "line": 616, "parent": 65}, {"file": 3}, {"command": 2, "file": 3, "line": 269, "parent": 67}, {"file": 4}, {"command": 2, "file": 4, "line": 57, "parent": 69}, {"command": 2, "file": 4, "line": 57, "parent": 69}, {"command": 2, "file": 4, "line": 57, "parent": 69}, {"command": 2, "file": 4, "line": 57, "parent": 69}, {"command": 2, "file": 4, "line": 57, "parent": 69}, {"command": 2, "file": 4, "line": 57, "parent": 69}, {"command": 2, "file": 4, "line": 57, "parent": 69}, {"command": 2, "file": 4, "line": 57, "parent": 69}, {"file": 5}, {"command": 2, "file": 5, "line": 18, "parent": 78}, {"file": 6}, {"command": 2, "file": 6, "line": 7, "parent": 80}, {"file": 7}, {"command": 2, "file": 7, "line": 21, "parent": 82}, {"file": 8}, {"command": 5, "file": 8, "line": 54, "parent": 84}, {"command": 2, "file": 8, "line": 60, "parent": 84}, {"command": 2, "file": 8, "line": 63, "parent": 84}, {"command": 7, "file": 8, "line": 72, "parent": 84}, {"file": 10, "parent": 88}, {"command": 6, "file": 10, "line": 12, "parent": 89}, {"command": 2, "file": 9, "line": 163, "parent": 90}, {"command": 2, "file": 9, "line": 166, "parent": 90}, {"command": 6, "file": 10, "line": 18, "parent": 89}, {"command": 2, "file": 9, "line": 166, "parent": 93}, {"command": 2, "file": 8, "line": 82, "parent": 84}, {"file": 11}, {"command": 6, "file": 11, "line": 82, "parent": 96}, {"command": 2, "file": 9, "line": 163, "parent": 97}, {"command": 2, "file": 9, "line": 166, "parent": 97}, {"command": 8, "file": 11, "line": 83, "parent": 96}, {"command": 6, "file": 11, "line": 70, "parent": 100}, {"command": 2, "file": 9, "line": 166, "parent": 101}, {"command": 8, "file": 11, "line": 85, "parent": 96}, {"command": 6, "file": 11, "line": 70, "parent": 103}, {"command": 2, "file": 9, "line": 166, "parent": 104}, {"command": 8, "file": 11, "line": 123, "parent": 96}, {"command": 6, "file": 11, "line": 70, "parent": 106}, {"command": 2, "file": 9, "line": 166, "parent": 107}, {"command": 8, "file": 11, "line": 124, "parent": 96}, {"command": 6, "file": 11, "line": 70, "parent": 109}, {"command": 2, "file": 9, "line": 166, "parent": 110}, {"command": 8, "file": 11, "line": 129, "parent": 96}, {"command": 6, "file": 11, "line": 70, "parent": 112}, {"command": 2, "file": 9, "line": 166, "parent": 113}, {"command": 2, "file": 11, "line": 246, "parent": 96}, {"file": 12}, {"command": 2, "file": 12, "line": 188, "parent": 116}, {"file": 13}, {"command": 5, "file": 13, "line": 7, "parent": 118}, {"command": 6, "file": 13, "line": 14, "parent": 118}, {"command": 2, "file": 9, "line": 163, "parent": 120}, {"command": 2, "file": 9, "line": 166, "parent": 120}, {"file": 14}, {"command": 2, "file": 14, "line": 154, "parent": 123}, {"command": 2, "file": 14, "line": 155, "parent": 123}, {"command": 2, "file": 14, "line": 175, "parent": 123}, {"file": 15}, {"command": 2, "file": 15, "line": 39, "parent": 127}, {"command": 2, "file": 15, "line": 49, "parent": 127}, {"file": 16}, {"command": 2, "file": 16, "line": 39, "parent": 130}, {"command": 2, "file": 16, "line": 39, "parent": 130}, {"command": 2, "file": 16, "line": 39, "parent": 130}, {"command": 2, "file": 16, "line": 39, "parent": 130}, {"command": 2, "file": 16, "line": 39, "parent": 130}, {"command": 2, "file": 16, "line": 39, "parent": 130}, {"command": 2, "file": 16, "line": 39, "parent": 130}, {"command": 2, "file": 16, "line": 39, "parent": 130}, {"command": 2, "file": 16, "line": 39, "parent": 130}, {"command": 2, "file": 16, "line": 39, "parent": 130}, {"command": 2, "file": 16, "line": 39, "parent": 130}, {"command": 2, "file": 16, "line": 39, "parent": 130}, {"command": 2, "file": 16, "line": 39, "parent": 130}, {"command": 2, "file": 16, "line": 39, "parent": 130}, {"command": 2, "file": 16, "line": 39, "parent": 130}, {"command": 2, "file": 16, "line": 39, "parent": 130}, {"command": 2, "file": 16, "line": 39, "parent": 130}, {"command": 2, "file": 16, "line": 39, "parent": 130}, {"command": 2, "file": 16, "line": 39, "parent": 130}, {"command": 2, "file": 16, "line": 39, "parent": 130}, {"command": 2, "file": 16, "line": 39, "parent": 130}, {"command": 2, "file": 16, "line": 39, "parent": 130}, {"command": 2, "file": 16, "line": 39, "parent": 130}, {"command": 2, "file": 16, "line": 39, "parent": 130}, {"command": 2, "file": 16, "line": 39, "parent": 130}, {"command": 2, "file": 16, "line": 39, "parent": 130}, {"command": 2, "file": 16, "line": 39, "parent": 130}, {"command": 2, "file": 16, "line": 39, "parent": 130}, {"command": 2, "file": 16, "line": 39, "parent": 130}, {"command": 2, "file": 16, "line": 44, "parent": 130}, {"command": 2, "file": 16, "line": 43, "parent": 130}, {"command": 2, "file": 16, "line": 53, "parent": 130}, {"command": 2, "file": 16, "line": 56, "parent": 130}, {"command": 2, "file": 16, "line": 60, "parent": 130}, {"file": 17}, {"command": 2, "file": 17, "line": 48, "parent": 165}, {"command": 2, "file": 17, "line": 52, "parent": 165}, {"command": 2, "file": 17, "line": 55, "parent": 165}, {"command": 2, "file": 17, "line": 60, "parent": 165}, {"command": 2, "file": 17, "line": 61, "parent": 165}, {"file": 18}, {"command": 2, "file": 18, "line": 20, "parent": 171}, {"command": 2, "file": 4, "line": 42, "parent": 69}, {"command": 9, "file": 0, "line": 577, "parent": 1}, {"command": 10, "file": 9, "line": 148, "parent": 93}, {"command": 9, "file": 19, "line": 78, "parent": 175}, {"command": 2, "file": 0, "line": 608, "parent": 1}, {"command": 2, "file": 0, "line": 608, "parent": 1}, {"command": 2, "file": 0, "line": 608, "parent": 1}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-mlongcalls -Wno-frame-address  -fdiagnostics-color=always"}], "defines": [{"backtrace": 8, "define": "MBEDTLS_CONFIG_FILE=\"mbedtls/esp_config.h\""}, {"backtrace": 3, "define": "SOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE"}, {"backtrace": 36, "define": "UNITY_INCLUDE_CONFIG_H"}], "includes": [{"backtrace": 3, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include"}, {"backtrace": 3, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include"}, {"backtrace": 3, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/config"}, {"backtrace": 3, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include"}, {"backtrace": 3, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/freertos/FreeRTOS-Kernel/include"}, {"backtrace": 3, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/freertos/esp_additions/include/freertos"}, {"backtrace": 3, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/freertos/FreeRTOS-Kernel/portable/xtensa/include"}, {"backtrace": 3, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/freertos/esp_additions/include"}, {"backtrace": 3, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include"}, {"backtrace": 3, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc"}, {"backtrace": 3, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32"}, {"backtrace": 3, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/."}, {"backtrace": 3, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include"}, {"backtrace": 3, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/heap/include"}, {"backtrace": 3, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include"}, {"backtrace": 3, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include"}, {"backtrace": 3, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/."}, {"backtrace": 3, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include"}, {"backtrace": 3, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/esp32/include"}, {"backtrace": 3, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/include"}, {"backtrace": 3, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/platform_port/include"}, {"backtrace": 3, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include"}, {"backtrace": 3, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32"}, {"backtrace": 3, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32"}, {"backtrace": 3, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include"}, {"backtrace": 3, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_system/include"}, {"backtrace": 3, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_system/port/soc"}, {"backtrace": 3, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_system/port/include/private"}, {"backtrace": 3, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/lwip/include"}, {"backtrace": 3, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/lwip/include/apps"}, {"backtrace": 3, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/lwip/include/apps/sntp"}, {"backtrace": 3, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/lwip/lwip/src/include"}, {"backtrace": 3, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/lwip/port/esp32/include"}, {"backtrace": 3, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/lwip/port/esp32/include/arch"}, {"backtrace": 4, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_ringbuf/include"}, {"backtrace": 5, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/include"}, {"backtrace": 5, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/esp32/include"}, {"backtrace": 6, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/driver/include"}, {"backtrace": 6, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/driver/deprecated"}, {"backtrace": 6, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/driver/esp32/include"}, {"backtrace": 6, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_pm/include"}, {"backtrace": 8, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/mbedtls/port/include"}, {"backtrace": 8, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/mbedtls/mbedtls/include"}, {"backtrace": 8, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/mbedtls/mbedtls/library"}, {"backtrace": 8, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/mbedtls/esp_crt_bundle/include"}, {"backtrace": 9, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_app_format/include"}, {"backtrace": 10, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/include"}, {"backtrace": 10, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/bootloader_flash/include"}, {"backtrace": 11, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_partition/include"}, {"backtrace": 12, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/app_update/include"}, {"backtrace": 13, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/spi_flash/include"}, {"backtrace": 14, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/pthread/include"}, {"backtrace": 26, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_timer/include"}, {"backtrace": 27, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/app_trace/include"}, {"backtrace": 28, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_event/include"}, {"backtrace": 29, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/nvs_flash/include"}, {"backtrace": 30, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_phy/include"}, {"backtrace": 30, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_phy/esp32/include"}, {"backtrace": 31, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/vfs/include"}, {"backtrace": 33, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_netif/include"}, {"backtrace": 34, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/wpa_supplicant/include"}, {"backtrace": 34, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/wpa_supplicant/port/include"}, {"backtrace": 34, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/wpa_supplicant/esp_supplicant/include"}, {"backtrace": 35, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_wifi/include"}, {"backtrace": 36, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/unity/include"}, {"backtrace": 36, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/unity/unity/src"}, {"backtrace": 37, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/cmock/CMock/src"}, {"backtrace": 38, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/console"}, {"backtrace": 39, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/http_parser"}, {"backtrace": 40, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp-tls"}, {"backtrace": 40, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp-tls/esp-tls-crypto"}, {"backtrace": 41, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_adc/include"}, {"backtrace": 41, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_adc/interface"}, {"backtrace": 41, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_adc/esp32/include"}, {"backtrace": 41, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_adc/deprecated/include"}, {"backtrace": 42, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_eth/include"}, {"backtrace": 43, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_gdbstub/include"}, {"backtrace": 43, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_gdbstub/xtensa"}, {"backtrace": 43, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_gdbstub/esp32"}, {"backtrace": 44, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hid/include"}, {"backtrace": 45, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/tcp_transport/include"}, {"backtrace": 46, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_http_client/include"}, {"backtrace": 47, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_http_server/include"}, {"backtrace": 48, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_https_ota/include"}, {"backtrace": 49, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_lcd/include"}, {"backtrace": 49, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_lcd/interface"}, {"backtrace": 50, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/protobuf-c/protobuf-c"}, {"backtrace": 51, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/protocomm/include/common"}, {"backtrace": 51, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/protocomm/include/security"}, {"backtrace": 51, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/protocomm/include/transports"}, {"backtrace": 52, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_local_ctrl/include"}, {"backtrace": 177, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_psram/include"}, {"backtrace": 53, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/espcoredump/include"}, {"backtrace": 53, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/espcoredump/include/port/xtensa"}, {"backtrace": 54, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/wear_levelling/include"}, {"backtrace": 55, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/sdmmc/include"}, {"backtrace": 56, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/fatfs/diskio"}, {"backtrace": 56, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/fatfs/vfs"}, {"backtrace": 56, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/fatfs/src"}, {"backtrace": 178, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/idf_test/include"}, {"backtrace": 178, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/idf_test/include/esp32"}, {"backtrace": 179, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/ieee802154/include"}, {"backtrace": 57, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/json/cJSON"}, {"backtrace": 58, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/mqtt/esp-mqtt/include"}, {"backtrace": 59, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/perfmon/include"}, {"backtrace": 60, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/spiffs/include"}, {"backtrace": 61, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/ulp/ulp_common/include"}, {"backtrace": 61, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/ulp/ulp_common/include/esp32"}, {"backtrace": 62, "path": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/wifi_provisioning/include"}], "language": "C", "sourceIndexes": [0]}], "dependencies": [{"backtrace": 3, "id": "__idf_xtensa::@1daaaad7abe597e030f9"}, {"backtrace": 174, "id": "_project_elf_src::@6890427a1f51a3e7e1df"}, {"backtrace": 176, "id": "__ldgen_output_sections.ld::@874df54e61e20112dfd4"}, {"backtrace": 27, "id": "__idf_app_trace::@dd7eb3aa23293586d3b8"}, {"backtrace": 36, "id": "__idf_unity::@fec746937506b5690992"}, {"backtrace": 37, "id": "__idf_cmock::@38f9eb59693b7394f54c"}, {"backtrace": 38, "id": "__idf_console::@4311ed8d271cc0ee1422"}, {"backtrace": 44, "id": "__idf_esp_hid::@15df40e16a5d8775dd9b"}, {"backtrace": 49, "id": "__idf_esp_lcd::@d5a84597e1633c9ab0d3"}, {"backtrace": 50, "id": "__idf_protobuf-c::@b14513c4bd859268ec22"}, {"backtrace": 51, "id": "__idf_protocomm::@d76d01c8dec06205e1fc"}, {"backtrace": 52, "id": "__idf_esp_local_ctrl::@d6aad58d2946e56208ef"}, {"backtrace": 53, "id": "__idf_espcoredump::@718b9a7d55ab42cfc0a3"}, {"backtrace": 54, "id": "__idf_wear_levelling::@cb21b0d7c0815cff11c6"}, {"backtrace": 55, "id": "__idf_sdmmc::@89ed5bca1750ffbc7db6"}, {"backtrace": 56, "id": "__idf_fatfs::@75ac1d9b25dacc8cc9e7"}, {"backtrace": 57, "id": "__idf_json::@c78a8bdc820334f01148"}, {"backtrace": 58, "id": "__idf_mqtt::@c1ab2c6a99226d1f4b56"}, {"backtrace": 59, "id": "__idf_perfmon::@aac0f647c32bb8f54a67"}, {"backtrace": 60, "id": "__idf_spiffs::@df881b71474064c86689"}, {"backtrace": 62, "id": "__idf_wifi_provisioning::@9d7e7ffe1b15746c1098"}, {"backtrace": 63, "id": "__idf_main::@7368607ed66887415643"}], "id": "libespidf.elf::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-mlongcalls -Wno-frame-address", "role": "flags"}, {"fragment": "", "role": "flags"}, {"backtrace": 3, "fragment": "esp-idf/xtensa/libxtensa.a", "role": "libraries"}, {"backtrace": 4, "fragment": "esp-idf/esp_ringbuf/libesp_ringbuf.a", "role": "libraries"}, {"backtrace": 5, "fragment": "esp-idf/efuse/libefuse.a", "role": "libraries"}, {"backtrace": 6, "fragment": "esp-idf/driver/libdriver.a", "role": "libraries"}, {"backtrace": 7, "fragment": "esp-idf/esp_pm/libesp_pm.a", "role": "libraries"}, {"backtrace": 8, "fragment": "esp-idf/mbedtls/libmbedtls.a", "role": "libraries"}, {"backtrace": 9, "fragment": "esp-idf/esp_app_format/libesp_app_format.a", "role": "libraries"}, {"backtrace": 10, "fragment": "esp-idf/bootloader_support/libbootloader_support.a", "role": "libraries"}, {"backtrace": 11, "fragment": "esp-idf/esp_partition/libesp_partition.a", "role": "libraries"}, {"backtrace": 12, "fragment": "esp-idf/app_update/libapp_update.a", "role": "libraries"}, {"backtrace": 13, "fragment": "esp-idf/spi_flash/libspi_flash.a", "role": "libraries"}, {"backtrace": 14, "fragment": "esp-idf/pthread/libpthread.a", "role": "libraries"}, {"backtrace": 15, "fragment": "esp-idf/esp_system/libesp_system.a", "role": "libraries"}, {"backtrace": 16, "fragment": "esp-idf/esp_rom/libesp_rom.a", "role": "libraries"}, {"backtrace": 17, "fragment": "esp-idf/hal/libhal.a", "role": "libraries"}, {"backtrace": 18, "fragment": "esp-idf/log/liblog.a", "role": "libraries"}, {"backtrace": 19, "fragment": "esp-idf/heap/libheap.a", "role": "libraries"}, {"backtrace": 20, "fragment": "esp-idf/soc/libsoc.a", "role": "libraries"}, {"backtrace": 21, "fragment": "esp-idf/esp_hw_support/libesp_hw_support.a", "role": "libraries"}, {"backtrace": 22, "fragment": "esp-idf/freertos/libfreertos.a", "role": "libraries"}, {"backtrace": 23, "fragment": "esp-idf/newlib/libnewlib.a", "role": "libraries"}, {"backtrace": 24, "fragment": "esp-idf/cxx/libcxx.a", "role": "libraries"}, {"backtrace": 25, "fragment": "esp-idf/esp_common/libesp_common.a", "role": "libraries"}, {"backtrace": 26, "fragment": "esp-idf/esp_timer/libesp_timer.a", "role": "libraries"}, {"backtrace": 27, "fragment": "esp-idf/app_trace/libapp_trace.a", "role": "libraries"}, {"backtrace": 28, "fragment": "esp-idf/esp_event/libesp_event.a", "role": "libraries"}, {"backtrace": 29, "fragment": "esp-idf/nvs_flash/libnvs_flash.a", "role": "libraries"}, {"backtrace": 30, "fragment": "esp-idf/esp_phy/libesp_phy.a", "role": "libraries"}, {"backtrace": 31, "fragment": "esp-idf/vfs/libvfs.a", "role": "libraries"}, {"backtrace": 32, "fragment": "esp-idf/lwip/liblwip.a", "role": "libraries"}, {"backtrace": 33, "fragment": "esp-idf/esp_netif/libesp_netif.a", "role": "libraries"}, {"backtrace": 34, "fragment": "esp-idf/wpa_supplicant/libwpa_supplicant.a", "role": "libraries"}, {"backtrace": 35, "fragment": "esp-idf/esp_wifi/libesp_wifi.a", "role": "libraries"}, {"backtrace": 36, "fragment": "esp-idf/unity/libunity.a", "role": "libraries"}, {"backtrace": 37, "fragment": "esp-idf/cmock/libcmock.a", "role": "libraries"}, {"backtrace": 38, "fragment": "esp-idf/console/libconsole.a", "role": "libraries"}, {"backtrace": 39, "fragment": "esp-idf/http_parser/libhttp_parser.a", "role": "libraries"}, {"backtrace": 40, "fragment": "esp-idf/esp-tls/libesp-tls.a", "role": "libraries"}, {"backtrace": 41, "fragment": "esp-idf/esp_adc/libesp_adc.a", "role": "libraries"}, {"backtrace": 42, "fragment": "esp-idf/esp_eth/libesp_eth.a", "role": "libraries"}, {"backtrace": 43, "fragment": "esp-idf/esp_gdbstub/libesp_gdbstub.a", "role": "libraries"}, {"backtrace": 44, "fragment": "esp-idf/esp_hid/libesp_hid.a", "role": "libraries"}, {"backtrace": 45, "fragment": "esp-idf/tcp_transport/libtcp_transport.a", "role": "libraries"}, {"backtrace": 46, "fragment": "esp-idf/esp_http_client/libesp_http_client.a", "role": "libraries"}, {"backtrace": 47, "fragment": "esp-idf/esp_http_server/libesp_http_server.a", "role": "libraries"}, {"backtrace": 48, "fragment": "esp-idf/esp_https_ota/libesp_https_ota.a", "role": "libraries"}, {"backtrace": 49, "fragment": "esp-idf/esp_lcd/libesp_lcd.a", "role": "libraries"}, {"backtrace": 50, "fragment": "esp-idf/protobuf-c/libprotobuf-c.a", "role": "libraries"}, {"backtrace": 51, "fragment": "esp-idf/protocomm/libprotocomm.a", "role": "libraries"}, {"backtrace": 52, "fragment": "esp-idf/esp_local_ctrl/libesp_local_ctrl.a", "role": "libraries"}, {"backtrace": 53, "fragment": "esp-idf/espcoredump/libespcoredump.a", "role": "libraries"}, {"backtrace": 54, "fragment": "esp-idf/wear_levelling/libwear_levelling.a", "role": "libraries"}, {"backtrace": 55, "fragment": "esp-idf/sdmmc/libsdmmc.a", "role": "libraries"}, {"backtrace": 56, "fragment": "esp-idf/fatfs/libfatfs.a", "role": "libraries"}, {"backtrace": 57, "fragment": "esp-idf/json/libjson.a", "role": "libraries"}, {"backtrace": 58, "fragment": "esp-idf/mqtt/libmqtt.a", "role": "libraries"}, {"backtrace": 59, "fragment": "esp-idf/perfmon/libperfmon.a", "role": "libraries"}, {"backtrace": 60, "fragment": "esp-idf/spiffs/libspiffs.a", "role": "libraries"}, {"backtrace": 61, "fragment": "esp-idf/ulp/libulp.a", "role": "libraries"}, {"backtrace": 62, "fragment": "esp-idf/wifi_provisioning/libwifi_provisioning.a", "role": "libraries"}, {"backtrace": 63, "fragment": "esp-idf/main/libmain.a", "role": "libraries"}, {"backtrace": 64, "fragment": "-Wl,--cref", "role": "libraries"}, {"backtrace": 64, "fragment": "-Wl,--defsym=IDF_TARGET_ESP32=0", "role": "libraries"}, {"backtrace": 64, "fragment": "-Wl,--Map=\"/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/libespidf.map\"", "role": "libraries"}, {"backtrace": 66, "fragment": "-fno-rtti", "role": "libraries"}, {"backtrace": 66, "fragment": "-fno-lto", "role": "libraries"}, {"backtrace": 66, "fragment": "-Wl,--gc-sections", "role": "libraries"}, {"backtrace": 66, "fragment": "-Wl,--warn-common", "role": "libraries"}, {"backtrace": 27, "fragment": "esp-idf/app_trace/libapp_trace.a", "role": "libraries"}, {"backtrace": 37, "fragment": "esp-idf/cmock/libcmock.a", "role": "libraries"}, {"backtrace": 36, "fragment": "esp-idf/unity/libunity.a", "role": "libraries"}, {"backtrace": 44, "fragment": "esp-idf/esp_hid/libesp_hid.a", "role": "libraries"}, {"backtrace": 49, "fragment": "esp-idf/esp_lcd/libesp_lcd.a", "role": "libraries"}, {"backtrace": 52, "fragment": "esp-idf/esp_local_ctrl/libesp_local_ctrl.a", "role": "libraries"}, {"backtrace": 53, "fragment": "esp-idf/espcoredump/libespcoredump.a", "role": "libraries"}, {"backtrace": 56, "fragment": "esp-idf/fatfs/libfatfs.a", "role": "libraries"}, {"backtrace": 54, "fragment": "esp-idf/wear_levelling/libwear_levelling.a", "role": "libraries"}, {"backtrace": 55, "fragment": "esp-idf/sdmmc/libsdmmc.a", "role": "libraries"}, {"backtrace": 58, "fragment": "esp-idf/mqtt/libmqtt.a", "role": "libraries"}, {"backtrace": 59, "fragment": "esp-idf/perfmon/libperfmon.a", "role": "libraries"}, {"backtrace": 60, "fragment": "esp-idf/spiffs/libspiffs.a", "role": "libraries"}, {"backtrace": 62, "fragment": "esp-idf/wifi_provisioning/libwifi_provisioning.a", "role": "libraries"}, {"backtrace": 51, "fragment": "esp-idf/protocomm/libprotocomm.a", "role": "libraries"}, {"backtrace": 38, "fragment": "esp-idf/console/libconsole.a", "role": "libraries"}, {"backtrace": 50, "fragment": "esp-idf/protobuf-c/libprotobuf-c.a", "role": "libraries"}, {"backtrace": 57, "fragment": "esp-idf/json/libjson.a", "role": "libraries"}, {"backtrace": 3, "fragment": "esp-idf/xtensa/libxtensa.a", "role": "libraries"}, {"backtrace": 4, "fragment": "esp-idf/esp_ringbuf/libesp_ringbuf.a", "role": "libraries"}, {"backtrace": 5, "fragment": "esp-idf/efuse/libefuse.a", "role": "libraries"}, {"backtrace": 6, "fragment": "esp-idf/driver/libdriver.a", "role": "libraries"}, {"backtrace": 7, "fragment": "esp-idf/esp_pm/libesp_pm.a", "role": "libraries"}, {"backtrace": 8, "fragment": "esp-idf/mbedtls/libmbedtls.a", "role": "libraries"}, {"backtrace": 9, "fragment": "esp-idf/esp_app_format/libesp_app_format.a", "role": "libraries"}, {"backtrace": 10, "fragment": "esp-idf/bootloader_support/libbootloader_support.a", "role": "libraries"}, {"backtrace": 11, "fragment": "esp-idf/esp_partition/libesp_partition.a", "role": "libraries"}, {"backtrace": 12, "fragment": "esp-idf/app_update/libapp_update.a", "role": "libraries"}, {"backtrace": 13, "fragment": "esp-idf/spi_flash/libspi_flash.a", "role": "libraries"}, {"backtrace": 14, "fragment": "esp-idf/pthread/libpthread.a", "role": "libraries"}, {"backtrace": 15, "fragment": "esp-idf/esp_system/libesp_system.a", "role": "libraries"}, {"backtrace": 16, "fragment": "esp-idf/esp_rom/libesp_rom.a", "role": "libraries"}, {"backtrace": 17, "fragment": "esp-idf/hal/libhal.a", "role": "libraries"}, {"backtrace": 18, "fragment": "esp-idf/log/liblog.a", "role": "libraries"}, {"backtrace": 19, "fragment": "esp-idf/heap/libheap.a", "role": "libraries"}, {"backtrace": 20, "fragment": "esp-idf/soc/libsoc.a", "role": "libraries"}, {"backtrace": 21, "fragment": "esp-idf/esp_hw_support/libesp_hw_support.a", "role": "libraries"}, {"backtrace": 22, "fragment": "esp-idf/freertos/libfreertos.a", "role": "libraries"}, {"backtrace": 23, "fragment": "esp-idf/newlib/libnewlib.a", "role": "libraries"}, {"backtrace": 24, "fragment": "esp-idf/cxx/libcxx.a", "role": "libraries"}, {"backtrace": 25, "fragment": "esp-idf/esp_common/libesp_common.a", "role": "libraries"}, {"backtrace": 26, "fragment": "esp-idf/esp_timer/libesp_timer.a", "role": "libraries"}, {"backtrace": 28, "fragment": "esp-idf/esp_event/libesp_event.a", "role": "libraries"}, {"backtrace": 29, "fragment": "esp-idf/nvs_flash/libnvs_flash.a", "role": "libraries"}, {"backtrace": 30, "fragment": "esp-idf/esp_phy/libesp_phy.a", "role": "libraries"}, {"backtrace": 31, "fragment": "esp-idf/vfs/libvfs.a", "role": "libraries"}, {"backtrace": 32, "fragment": "esp-idf/lwip/liblwip.a", "role": "libraries"}, {"backtrace": 33, "fragment": "esp-idf/esp_netif/libesp_netif.a", "role": "libraries"}, {"backtrace": 34, "fragment": "esp-idf/wpa_supplicant/libwpa_supplicant.a", "role": "libraries"}, {"backtrace": 35, "fragment": "esp-idf/esp_wifi/libesp_wifi.a", "role": "libraries"}, {"backtrace": 39, "fragment": "esp-idf/http_parser/libhttp_parser.a", "role": "libraries"}, {"backtrace": 40, "fragment": "esp-idf/esp-tls/libesp-tls.a", "role": "libraries"}, {"backtrace": 41, "fragment": "esp-idf/esp_adc/libesp_adc.a", "role": "libraries"}, {"backtrace": 42, "fragment": "esp-idf/esp_eth/libesp_eth.a", "role": "libraries"}, {"backtrace": 43, "fragment": "esp-idf/esp_gdbstub/libesp_gdbstub.a", "role": "libraries"}, {"backtrace": 45, "fragment": "esp-idf/tcp_transport/libtcp_transport.a", "role": "libraries"}, {"backtrace": 46, "fragment": "esp-idf/esp_http_client/libesp_http_client.a", "role": "libraries"}, {"backtrace": 47, "fragment": "esp-idf/esp_http_server/libesp_http_server.a", "role": "libraries"}, {"backtrace": 48, "fragment": "esp-idf/esp_https_ota/libesp_https_ota.a", "role": "libraries"}, {"backtrace": 61, "fragment": "esp-idf/ulp/libulp.a", "role": "libraries"}, {"backtrace": 68, "fragment": "esp-idf/mbedtls/mbedtls/library/libmbedtls.a", "role": "libraries"}, {"backtrace": 68, "fragment": "esp-idf/mbedtls/mbedtls/library/libmbedcrypto.a", "role": "libraries"}, {"backtrace": 68, "fragment": "esp-idf/mbedtls/mbedtls/library/libmbedx509.a", "role": "libraries"}, {"backtrace": 70, "fragment": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_wifi/lib/esp32/libcoexist.a", "role": "libraries"}, {"backtrace": 71, "fragment": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_wifi/lib/esp32/libcore.a", "role": "libraries"}, {"backtrace": 72, "fragment": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_wifi/lib/esp32/libespnow.a", "role": "libraries"}, {"backtrace": 73, "fragment": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_wifi/lib/esp32/libmesh.a", "role": "libraries"}, {"backtrace": 74, "fragment": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_wifi/lib/esp32/libnet80211.a", "role": "libraries"}, {"backtrace": 75, "fragment": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_wifi/lib/esp32/libpp.a", "role": "libraries"}, {"backtrace": 76, "fragment": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_wifi/lib/esp32/libsmartconfig.a", "role": "libraries"}, {"backtrace": 77, "fragment": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_wifi/lib/esp32/libwapi.a", "role": "libraries"}, {"backtrace": 3, "fragment": "esp-idf/xtensa/libxtensa.a", "role": "libraries"}, {"backtrace": 4, "fragment": "esp-idf/esp_ringbuf/libesp_ringbuf.a", "role": "libraries"}, {"backtrace": 5, "fragment": "esp-idf/efuse/libefuse.a", "role": "libraries"}, {"backtrace": 6, "fragment": "esp-idf/driver/libdriver.a", "role": "libraries"}, {"backtrace": 7, "fragment": "esp-idf/esp_pm/libesp_pm.a", "role": "libraries"}, {"backtrace": 8, "fragment": "esp-idf/mbedtls/libmbedtls.a", "role": "libraries"}, {"backtrace": 9, "fragment": "esp-idf/esp_app_format/libesp_app_format.a", "role": "libraries"}, {"backtrace": 10, "fragment": "esp-idf/bootloader_support/libbootloader_support.a", "role": "libraries"}, {"backtrace": 11, "fragment": "esp-idf/esp_partition/libesp_partition.a", "role": "libraries"}, {"backtrace": 12, "fragment": "esp-idf/app_update/libapp_update.a", "role": "libraries"}, {"backtrace": 13, "fragment": "esp-idf/spi_flash/libspi_flash.a", "role": "libraries"}, {"backtrace": 14, "fragment": "esp-idf/pthread/libpthread.a", "role": "libraries"}, {"backtrace": 15, "fragment": "esp-idf/esp_system/libesp_system.a", "role": "libraries"}, {"backtrace": 16, "fragment": "esp-idf/esp_rom/libesp_rom.a", "role": "libraries"}, {"backtrace": 17, "fragment": "esp-idf/hal/libhal.a", "role": "libraries"}, {"backtrace": 18, "fragment": "esp-idf/log/liblog.a", "role": "libraries"}, {"backtrace": 19, "fragment": "esp-idf/heap/libheap.a", "role": "libraries"}, {"backtrace": 20, "fragment": "esp-idf/soc/libsoc.a", "role": "libraries"}, {"backtrace": 21, "fragment": "esp-idf/esp_hw_support/libesp_hw_support.a", "role": "libraries"}, {"backtrace": 22, "fragment": "esp-idf/freertos/libfreertos.a", "role": "libraries"}, {"backtrace": 23, "fragment": "esp-idf/newlib/libnewlib.a", "role": "libraries"}, {"backtrace": 24, "fragment": "esp-idf/cxx/libcxx.a", "role": "libraries"}, {"backtrace": 25, "fragment": "esp-idf/esp_common/libesp_common.a", "role": "libraries"}, {"backtrace": 26, "fragment": "esp-idf/esp_timer/libesp_timer.a", "role": "libraries"}, {"backtrace": 28, "fragment": "esp-idf/esp_event/libesp_event.a", "role": "libraries"}, {"backtrace": 29, "fragment": "esp-idf/nvs_flash/libnvs_flash.a", "role": "libraries"}, {"backtrace": 30, "fragment": "esp-idf/esp_phy/libesp_phy.a", "role": "libraries"}, {"backtrace": 31, "fragment": "esp-idf/vfs/libvfs.a", "role": "libraries"}, {"backtrace": 32, "fragment": "esp-idf/lwip/liblwip.a", "role": "libraries"}, {"backtrace": 33, "fragment": "esp-idf/esp_netif/libesp_netif.a", "role": "libraries"}, {"backtrace": 34, "fragment": "esp-idf/wpa_supplicant/libwpa_supplicant.a", "role": "libraries"}, {"backtrace": 35, "fragment": "esp-idf/esp_wifi/libesp_wifi.a", "role": "libraries"}, {"backtrace": 39, "fragment": "esp-idf/http_parser/libhttp_parser.a", "role": "libraries"}, {"backtrace": 40, "fragment": "esp-idf/esp-tls/libesp-tls.a", "role": "libraries"}, {"backtrace": 41, "fragment": "esp-idf/esp_adc/libesp_adc.a", "role": "libraries"}, {"backtrace": 42, "fragment": "esp-idf/esp_eth/libesp_eth.a", "role": "libraries"}, {"backtrace": 43, "fragment": "esp-idf/esp_gdbstub/libesp_gdbstub.a", "role": "libraries"}, {"backtrace": 45, "fragment": "esp-idf/tcp_transport/libtcp_transport.a", "role": "libraries"}, {"backtrace": 46, "fragment": "esp-idf/esp_http_client/libesp_http_client.a", "role": "libraries"}, {"backtrace": 47, "fragment": "esp-idf/esp_http_server/libesp_http_server.a", "role": "libraries"}, {"backtrace": 48, "fragment": "esp-idf/esp_https_ota/libesp_https_ota.a", "role": "libraries"}, {"backtrace": 61, "fragment": "esp-idf/ulp/libulp.a", "role": "libraries"}, {"backtrace": 68, "fragment": "esp-idf/mbedtls/mbedtls/library/libmbedtls.a", "role": "libraries"}, {"backtrace": 68, "fragment": "esp-idf/mbedtls/mbedtls/library/libmbedcrypto.a", "role": "libraries"}, {"backtrace": 68, "fragment": "esp-idf/mbedtls/mbedtls/library/libmbedx509.a", "role": "libraries"}, {"backtrace": 70, "fragment": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_wifi/lib/esp32/libcoexist.a", "role": "libraries"}, {"backtrace": 71, "fragment": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_wifi/lib/esp32/libcore.a", "role": "libraries"}, {"backtrace": 72, "fragment": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_wifi/lib/esp32/libespnow.a", "role": "libraries"}, {"backtrace": 73, "fragment": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_wifi/lib/esp32/libmesh.a", "role": "libraries"}, {"backtrace": 74, "fragment": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_wifi/lib/esp32/libnet80211.a", "role": "libraries"}, {"backtrace": 75, "fragment": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_wifi/lib/esp32/libpp.a", "role": "libraries"}, {"backtrace": 76, "fragment": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_wifi/lib/esp32/libsmartconfig.a", "role": "libraries"}, {"backtrace": 77, "fragment": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_wifi/lib/esp32/libwapi.a", "role": "libraries"}, {"backtrace": 3, "fragment": "esp-idf/xtensa/libxtensa.a", "role": "libraries"}, {"backtrace": 4, "fragment": "esp-idf/esp_ringbuf/libesp_ringbuf.a", "role": "libraries"}, {"backtrace": 5, "fragment": "esp-idf/efuse/libefuse.a", "role": "libraries"}, {"backtrace": 6, "fragment": "esp-idf/driver/libdriver.a", "role": "libraries"}, {"backtrace": 7, "fragment": "esp-idf/esp_pm/libesp_pm.a", "role": "libraries"}, {"backtrace": 8, "fragment": "esp-idf/mbedtls/libmbedtls.a", "role": "libraries"}, {"backtrace": 9, "fragment": "esp-idf/esp_app_format/libesp_app_format.a", "role": "libraries"}, {"backtrace": 10, "fragment": "esp-idf/bootloader_support/libbootloader_support.a", "role": "libraries"}, {"backtrace": 11, "fragment": "esp-idf/esp_partition/libesp_partition.a", "role": "libraries"}, {"backtrace": 12, "fragment": "esp-idf/app_update/libapp_update.a", "role": "libraries"}, {"backtrace": 13, "fragment": "esp-idf/spi_flash/libspi_flash.a", "role": "libraries"}, {"backtrace": 14, "fragment": "esp-idf/pthread/libpthread.a", "role": "libraries"}, {"backtrace": 15, "fragment": "esp-idf/esp_system/libesp_system.a", "role": "libraries"}, {"backtrace": 16, "fragment": "esp-idf/esp_rom/libesp_rom.a", "role": "libraries"}, {"backtrace": 17, "fragment": "esp-idf/hal/libhal.a", "role": "libraries"}, {"backtrace": 18, "fragment": "esp-idf/log/liblog.a", "role": "libraries"}, {"backtrace": 19, "fragment": "esp-idf/heap/libheap.a", "role": "libraries"}, {"backtrace": 20, "fragment": "esp-idf/soc/libsoc.a", "role": "libraries"}, {"backtrace": 21, "fragment": "esp-idf/esp_hw_support/libesp_hw_support.a", "role": "libraries"}, {"backtrace": 22, "fragment": "esp-idf/freertos/libfreertos.a", "role": "libraries"}, {"backtrace": 23, "fragment": "esp-idf/newlib/libnewlib.a", "role": "libraries"}, {"backtrace": 24, "fragment": "esp-idf/cxx/libcxx.a", "role": "libraries"}, {"backtrace": 25, "fragment": "esp-idf/esp_common/libesp_common.a", "role": "libraries"}, {"backtrace": 26, "fragment": "esp-idf/esp_timer/libesp_timer.a", "role": "libraries"}, {"backtrace": 28, "fragment": "esp-idf/esp_event/libesp_event.a", "role": "libraries"}, {"backtrace": 29, "fragment": "esp-idf/nvs_flash/libnvs_flash.a", "role": "libraries"}, {"backtrace": 30, "fragment": "esp-idf/esp_phy/libesp_phy.a", "role": "libraries"}, {"backtrace": 31, "fragment": "esp-idf/vfs/libvfs.a", "role": "libraries"}, {"backtrace": 32, "fragment": "esp-idf/lwip/liblwip.a", "role": "libraries"}, {"backtrace": 33, "fragment": "esp-idf/esp_netif/libesp_netif.a", "role": "libraries"}, {"backtrace": 34, "fragment": "esp-idf/wpa_supplicant/libwpa_supplicant.a", "role": "libraries"}, {"backtrace": 35, "fragment": "esp-idf/esp_wifi/libesp_wifi.a", "role": "libraries"}, {"backtrace": 39, "fragment": "esp-idf/http_parser/libhttp_parser.a", "role": "libraries"}, {"backtrace": 40, "fragment": "esp-idf/esp-tls/libesp-tls.a", "role": "libraries"}, {"backtrace": 41, "fragment": "esp-idf/esp_adc/libesp_adc.a", "role": "libraries"}, {"backtrace": 42, "fragment": "esp-idf/esp_eth/libesp_eth.a", "role": "libraries"}, {"backtrace": 43, "fragment": "esp-idf/esp_gdbstub/libesp_gdbstub.a", "role": "libraries"}, {"backtrace": 45, "fragment": "esp-idf/tcp_transport/libtcp_transport.a", "role": "libraries"}, {"backtrace": 46, "fragment": "esp-idf/esp_http_client/libesp_http_client.a", "role": "libraries"}, {"backtrace": 47, "fragment": "esp-idf/esp_http_server/libesp_http_server.a", "role": "libraries"}, {"backtrace": 48, "fragment": "esp-idf/esp_https_ota/libesp_https_ota.a", "role": "libraries"}, {"backtrace": 61, "fragment": "esp-idf/ulp/libulp.a", "role": "libraries"}, {"backtrace": 68, "fragment": "esp-idf/mbedtls/mbedtls/library/libmbedtls.a", "role": "libraries"}, {"backtrace": 68, "fragment": "esp-idf/mbedtls/mbedtls/library/libmbedcrypto.a", "role": "libraries"}, {"backtrace": 68, "fragment": "esp-idf/mbedtls/mbedtls/library/libmbedx509.a", "role": "libraries"}, {"backtrace": 70, "fragment": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_wifi/lib/esp32/libcoexist.a", "role": "libraries"}, {"backtrace": 71, "fragment": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_wifi/lib/esp32/libcore.a", "role": "libraries"}, {"backtrace": 72, "fragment": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_wifi/lib/esp32/libespnow.a", "role": "libraries"}, {"backtrace": 73, "fragment": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_wifi/lib/esp32/libmesh.a", "role": "libraries"}, {"backtrace": 74, "fragment": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_wifi/lib/esp32/libnet80211.a", "role": "libraries"}, {"backtrace": 75, "fragment": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_wifi/lib/esp32/libpp.a", "role": "libraries"}, {"backtrace": 76, "fragment": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_wifi/lib/esp32/libsmartconfig.a", "role": "libraries"}, {"backtrace": 77, "fragment": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_wifi/lib/esp32/libwapi.a", "role": "libraries"}, {"backtrace": 3, "fragment": "esp-idf/xtensa/libxtensa.a", "role": "libraries"}, {"backtrace": 4, "fragment": "esp-idf/esp_ringbuf/libesp_ringbuf.a", "role": "libraries"}, {"backtrace": 5, "fragment": "esp-idf/efuse/libefuse.a", "role": "libraries"}, {"backtrace": 6, "fragment": "esp-idf/driver/libdriver.a", "role": "libraries"}, {"backtrace": 7, "fragment": "esp-idf/esp_pm/libesp_pm.a", "role": "libraries"}, {"backtrace": 8, "fragment": "esp-idf/mbedtls/libmbedtls.a", "role": "libraries"}, {"backtrace": 9, "fragment": "esp-idf/esp_app_format/libesp_app_format.a", "role": "libraries"}, {"backtrace": 10, "fragment": "esp-idf/bootloader_support/libbootloader_support.a", "role": "libraries"}, {"backtrace": 11, "fragment": "esp-idf/esp_partition/libesp_partition.a", "role": "libraries"}, {"backtrace": 12, "fragment": "esp-idf/app_update/libapp_update.a", "role": "libraries"}, {"backtrace": 13, "fragment": "esp-idf/spi_flash/libspi_flash.a", "role": "libraries"}, {"backtrace": 14, "fragment": "esp-idf/pthread/libpthread.a", "role": "libraries"}, {"backtrace": 15, "fragment": "esp-idf/esp_system/libesp_system.a", "role": "libraries"}, {"backtrace": 16, "fragment": "esp-idf/esp_rom/libesp_rom.a", "role": "libraries"}, {"backtrace": 17, "fragment": "esp-idf/hal/libhal.a", "role": "libraries"}, {"backtrace": 18, "fragment": "esp-idf/log/liblog.a", "role": "libraries"}, {"backtrace": 19, "fragment": "esp-idf/heap/libheap.a", "role": "libraries"}, {"backtrace": 20, "fragment": "esp-idf/soc/libsoc.a", "role": "libraries"}, {"backtrace": 21, "fragment": "esp-idf/esp_hw_support/libesp_hw_support.a", "role": "libraries"}, {"backtrace": 22, "fragment": "esp-idf/freertos/libfreertos.a", "role": "libraries"}, {"backtrace": 23, "fragment": "esp-idf/newlib/libnewlib.a", "role": "libraries"}, {"backtrace": 24, "fragment": "esp-idf/cxx/libcxx.a", "role": "libraries"}, {"backtrace": 25, "fragment": "esp-idf/esp_common/libesp_common.a", "role": "libraries"}, {"backtrace": 26, "fragment": "esp-idf/esp_timer/libesp_timer.a", "role": "libraries"}, {"backtrace": 28, "fragment": "esp-idf/esp_event/libesp_event.a", "role": "libraries"}, {"backtrace": 29, "fragment": "esp-idf/nvs_flash/libnvs_flash.a", "role": "libraries"}, {"backtrace": 30, "fragment": "esp-idf/esp_phy/libesp_phy.a", "role": "libraries"}, {"backtrace": 31, "fragment": "esp-idf/vfs/libvfs.a", "role": "libraries"}, {"backtrace": 32, "fragment": "esp-idf/lwip/liblwip.a", "role": "libraries"}, {"backtrace": 33, "fragment": "esp-idf/esp_netif/libesp_netif.a", "role": "libraries"}, {"backtrace": 34, "fragment": "esp-idf/wpa_supplicant/libwpa_supplicant.a", "role": "libraries"}, {"backtrace": 35, "fragment": "esp-idf/esp_wifi/libesp_wifi.a", "role": "libraries"}, {"backtrace": 39, "fragment": "esp-idf/http_parser/libhttp_parser.a", "role": "libraries"}, {"backtrace": 40, "fragment": "esp-idf/esp-tls/libesp-tls.a", "role": "libraries"}, {"backtrace": 41, "fragment": "esp-idf/esp_adc/libesp_adc.a", "role": "libraries"}, {"backtrace": 42, "fragment": "esp-idf/esp_eth/libesp_eth.a", "role": "libraries"}, {"backtrace": 43, "fragment": "esp-idf/esp_gdbstub/libesp_gdbstub.a", "role": "libraries"}, {"backtrace": 45, "fragment": "esp-idf/tcp_transport/libtcp_transport.a", "role": "libraries"}, {"backtrace": 46, "fragment": "esp-idf/esp_http_client/libesp_http_client.a", "role": "libraries"}, {"backtrace": 47, "fragment": "esp-idf/esp_http_server/libesp_http_server.a", "role": "libraries"}, {"backtrace": 48, "fragment": "esp-idf/esp_https_ota/libesp_https_ota.a", "role": "libraries"}, {"backtrace": 61, "fragment": "esp-idf/ulp/libulp.a", "role": "libraries"}, {"backtrace": 68, "fragment": "esp-idf/mbedtls/mbedtls/library/libmbedtls.a", "role": "libraries"}, {"backtrace": 68, "fragment": "esp-idf/mbedtls/mbedtls/library/libmbedcrypto.a", "role": "libraries"}, {"backtrace": 68, "fragment": "esp-idf/mbedtls/mbedtls/library/libmbedx509.a", "role": "libraries"}, {"backtrace": 70, "fragment": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_wifi/lib/esp32/libcoexist.a", "role": "libraries"}, {"backtrace": 71, "fragment": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_wifi/lib/esp32/libcore.a", "role": "libraries"}, {"backtrace": 72, "fragment": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_wifi/lib/esp32/libespnow.a", "role": "libraries"}, {"backtrace": 73, "fragment": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_wifi/lib/esp32/libmesh.a", "role": "libraries"}, {"backtrace": 74, "fragment": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_wifi/lib/esp32/libnet80211.a", "role": "libraries"}, {"backtrace": 75, "fragment": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_wifi/lib/esp32/libpp.a", "role": "libraries"}, {"backtrace": 76, "fragment": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_wifi/lib/esp32/libsmartconfig.a", "role": "libraries"}, {"backtrace": 77, "fragment": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_wifi/lib/esp32/libwapi.a", "role": "libraries"}, {"backtrace": 3, "fragment": "esp-idf/xtensa/libxtensa.a", "role": "libraries"}, {"backtrace": 4, "fragment": "esp-idf/esp_ringbuf/libesp_ringbuf.a", "role": "libraries"}, {"backtrace": 5, "fragment": "esp-idf/efuse/libefuse.a", "role": "libraries"}, {"backtrace": 6, "fragment": "esp-idf/driver/libdriver.a", "role": "libraries"}, {"backtrace": 7, "fragment": "esp-idf/esp_pm/libesp_pm.a", "role": "libraries"}, {"backtrace": 8, "fragment": "esp-idf/mbedtls/libmbedtls.a", "role": "libraries"}, {"backtrace": 9, "fragment": "esp-idf/esp_app_format/libesp_app_format.a", "role": "libraries"}, {"backtrace": 10, "fragment": "esp-idf/bootloader_support/libbootloader_support.a", "role": "libraries"}, {"backtrace": 11, "fragment": "esp-idf/esp_partition/libesp_partition.a", "role": "libraries"}, {"backtrace": 12, "fragment": "esp-idf/app_update/libapp_update.a", "role": "libraries"}, {"backtrace": 13, "fragment": "esp-idf/spi_flash/libspi_flash.a", "role": "libraries"}, {"backtrace": 14, "fragment": "esp-idf/pthread/libpthread.a", "role": "libraries"}, {"backtrace": 15, "fragment": "esp-idf/esp_system/libesp_system.a", "role": "libraries"}, {"backtrace": 16, "fragment": "esp-idf/esp_rom/libesp_rom.a", "role": "libraries"}, {"backtrace": 17, "fragment": "esp-idf/hal/libhal.a", "role": "libraries"}, {"backtrace": 18, "fragment": "esp-idf/log/liblog.a", "role": "libraries"}, {"backtrace": 19, "fragment": "esp-idf/heap/libheap.a", "role": "libraries"}, {"backtrace": 20, "fragment": "esp-idf/soc/libsoc.a", "role": "libraries"}, {"backtrace": 21, "fragment": "esp-idf/esp_hw_support/libesp_hw_support.a", "role": "libraries"}, {"backtrace": 22, "fragment": "esp-idf/freertos/libfreertos.a", "role": "libraries"}, {"backtrace": 23, "fragment": "esp-idf/newlib/libnewlib.a", "role": "libraries"}, {"backtrace": 24, "fragment": "esp-idf/cxx/libcxx.a", "role": "libraries"}, {"backtrace": 25, "fragment": "esp-idf/esp_common/libesp_common.a", "role": "libraries"}, {"backtrace": 26, "fragment": "esp-idf/esp_timer/libesp_timer.a", "role": "libraries"}, {"backtrace": 28, "fragment": "esp-idf/esp_event/libesp_event.a", "role": "libraries"}, {"backtrace": 29, "fragment": "esp-idf/nvs_flash/libnvs_flash.a", "role": "libraries"}, {"backtrace": 30, "fragment": "esp-idf/esp_phy/libesp_phy.a", "role": "libraries"}, {"backtrace": 31, "fragment": "esp-idf/vfs/libvfs.a", "role": "libraries"}, {"backtrace": 32, "fragment": "esp-idf/lwip/liblwip.a", "role": "libraries"}, {"backtrace": 33, "fragment": "esp-idf/esp_netif/libesp_netif.a", "role": "libraries"}, {"backtrace": 34, "fragment": "esp-idf/wpa_supplicant/libwpa_supplicant.a", "role": "libraries"}, {"backtrace": 35, "fragment": "esp-idf/esp_wifi/libesp_wifi.a", "role": "libraries"}, {"backtrace": 39, "fragment": "esp-idf/http_parser/libhttp_parser.a", "role": "libraries"}, {"backtrace": 40, "fragment": "esp-idf/esp-tls/libesp-tls.a", "role": "libraries"}, {"backtrace": 41, "fragment": "esp-idf/esp_adc/libesp_adc.a", "role": "libraries"}, {"backtrace": 42, "fragment": "esp-idf/esp_eth/libesp_eth.a", "role": "libraries"}, {"backtrace": 43, "fragment": "esp-idf/esp_gdbstub/libesp_gdbstub.a", "role": "libraries"}, {"backtrace": 45, "fragment": "esp-idf/tcp_transport/libtcp_transport.a", "role": "libraries"}, {"backtrace": 46, "fragment": "esp-idf/esp_http_client/libesp_http_client.a", "role": "libraries"}, {"backtrace": 47, "fragment": "esp-idf/esp_http_server/libesp_http_server.a", "role": "libraries"}, {"backtrace": 48, "fragment": "esp-idf/esp_https_ota/libesp_https_ota.a", "role": "libraries"}, {"backtrace": 61, "fragment": "esp-idf/ulp/libulp.a", "role": "libraries"}, {"backtrace": 68, "fragment": "esp-idf/mbedtls/mbedtls/library/libmbedtls.a", "role": "libraries"}, {"backtrace": 68, "fragment": "esp-idf/mbedtls/mbedtls/library/libmbedcrypto.a", "role": "libraries"}, {"backtrace": 68, "fragment": "esp-idf/mbedtls/mbedtls/library/libmbedx509.a", "role": "libraries"}, {"backtrace": 70, "fragment": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_wifi/lib/esp32/libcoexist.a", "role": "libraries"}, {"backtrace": 71, "fragment": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_wifi/lib/esp32/libcore.a", "role": "libraries"}, {"backtrace": 72, "fragment": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_wifi/lib/esp32/libespnow.a", "role": "libraries"}, {"backtrace": 73, "fragment": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_wifi/lib/esp32/libmesh.a", "role": "libraries"}, {"backtrace": 74, "fragment": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_wifi/lib/esp32/libnet80211.a", "role": "libraries"}, {"backtrace": 75, "fragment": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_wifi/lib/esp32/libpp.a", "role": "libraries"}, {"backtrace": 76, "fragment": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_wifi/lib/esp32/libsmartconfig.a", "role": "libraries"}, {"backtrace": 77, "fragment": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_wifi/lib/esp32/libwapi.a", "role": "libraries"}, {"backtrace": 79, "fragment": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/libxt_hal.a", "role": "libraries"}, {"backtrace": 81, "fragment": "-u esp_app_desc", "role": "libraries"}, {"backtrace": 83, "fragment": "-u pthread_include_pthread_impl", "role": "libraries"}, {"backtrace": 83, "fragment": "-u pthread_include_pthread_cond_impl", "role": "libraries"}, {"backtrace": 83, "fragment": "-u pthread_include_pthread_local_storage_impl", "role": "libraries"}, {"backtrace": 83, "fragment": "-u pthread_include_pthread_rwlock_impl", "role": "libraries"}, {"backtrace": 85, "fragment": "-u ld_include_highint_hdl", "role": "libraries"}, {"backtrace": 86, "fragment": "-u start_app", "role": "libraries"}, {"backtrace": 87, "fragment": "-u start_app_other_cores", "role": "libraries"}, {"backtrace": 91, "fragment": "-L \"/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/esp-idf/esp_system/ld\"", "role": "libraries"}, {"backtrace": 92, "fragment": "-T memory.ld", "role": "libraries"}, {"backtrace": 94, "fragment": "-T sections.ld", "role": "libraries"}, {"backtrace": 95, "fragment": "-u __ubsan_include", "role": "libraries"}, {"backtrace": 98, "fragment": "-L \"/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32/ld\"", "role": "libraries"}, {"backtrace": 99, "fragment": "-T esp32.rom.ld", "role": "libraries"}, {"backtrace": 102, "fragment": "-T esp32.rom.api.ld", "role": "libraries"}, {"backtrace": 105, "fragment": "-T esp32.rom.libgcc.ld", "role": "libraries"}, {"backtrace": 108, "fragment": "-T esp32.rom.newlib-data.ld", "role": "libraries"}, {"backtrace": 111, "fragment": "-T esp32.rom.syscalls.ld", "role": "libraries"}, {"backtrace": 114, "fragment": "-T esp32.rom.newlib-funcs.ld", "role": "libraries"}, {"backtrace": 115, "fragment": "-Wl,--wrap=longjmp", "role": "libraries"}, {"backtrace": 117, "fragment": "-u __assert_func", "role": "libraries"}, {"backtrace": 119, "fragment": "-u esp_dport_access_reg_read", "role": "libraries"}, {"backtrace": 121, "fragment": "-L \"/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/ld\"", "role": "libraries"}, {"backtrace": 122, "fragment": "-T esp32.peripherals.ld", "role": "libraries"}, {"backtrace": 124, "fragment": "-Wl,--undefined=uxTopUsedPriority", "role": "libraries"}, {"backtrace": 125, "fragment": "-Wl,--undefined=FreeRTOS_openocd_params", "role": "libraries"}, {"backtrace": 126, "fragment": "-u app_main", "role": "libraries"}, {"backtrace": 128, "fragment": "-lc", "role": "libraries"}, {"backtrace": 128, "fragment": "-lm", "role": "libraries"}, {"backtrace": 128, "fragment": "esp-idf/newlib/libnewlib.a", "role": "libraries"}, {"backtrace": 129, "fragment": "-u newlib_include_heap_impl", "role": "libraries"}, {"backtrace": 129, "fragment": "-u newlib_include_syscalls_impl", "role": "libraries"}, {"backtrace": 129, "fragment": "-u newlib_include_pthread_impl", "role": "libraries"}, {"backtrace": 129, "fragment": "-u newlib_include_assert_impl", "role": "libraries"}, {"backtrace": 131, "fragment": "-Wl,--wrap=_Unwind_SetEnableExceptionFdeSorting", "role": "libraries"}, {"backtrace": 132, "fragment": "-Wl,--wrap=__register_frame_info_bases", "role": "libraries"}, {"backtrace": 133, "fragment": "-Wl,--wrap=__register_frame_info", "role": "libraries"}, {"backtrace": 134, "fragment": "-Wl,--wrap=__register_frame", "role": "libraries"}, {"backtrace": 135, "fragment": "-Wl,--wrap=__register_frame_info_table_bases", "role": "libraries"}, {"backtrace": 136, "fragment": "-Wl,--wrap=__register_frame_info_table", "role": "libraries"}, {"backtrace": 137, "fragment": "-Wl,--wrap=__register_frame_table", "role": "libraries"}, {"backtrace": 138, "fragment": "-Wl,--wrap=__deregister_frame_info_bases", "role": "libraries"}, {"backtrace": 139, "fragment": "-Wl,--wrap=__deregister_frame_info", "role": "libraries"}, {"backtrace": 140, "fragment": "-Wl,--wrap=_Unwind_Find_FDE", "role": "libraries"}, {"backtrace": 141, "fragment": "-Wl,--wrap=_Unwind_GetGR", "role": "libraries"}, {"backtrace": 142, "fragment": "-Wl,--wrap=_Unwind_GetCFA", "role": "libraries"}, {"backtrace": 143, "fragment": "-Wl,--wrap=_Unwind_GetIP", "role": "libraries"}, {"backtrace": 144, "fragment": "-Wl,--wrap=_Unwind_GetIPInfo", "role": "libraries"}, {"backtrace": 145, "fragment": "-Wl,--wrap=_Unwind_GetRegionStart", "role": "libraries"}, {"backtrace": 146, "fragment": "-Wl,--wrap=_Unwind_GetDataRelBase", "role": "libraries"}, {"backtrace": 147, "fragment": "-Wl,--wrap=_Unwind_GetTextRelBase", "role": "libraries"}, {"backtrace": 148, "fragment": "-Wl,--wrap=_Unwind_SetIP", "role": "libraries"}, {"backtrace": 149, "fragment": "-Wl,--wrap=_Unwind_SetGR", "role": "libraries"}, {"backtrace": 150, "fragment": "-Wl,--wrap=_Unwind_GetLanguageSpecificData", "role": "libraries"}, {"backtrace": 151, "fragment": "-Wl,--wrap=_Unwind_FindEnclosingFunction", "role": "libraries"}, {"backtrace": 152, "fragment": "-Wl,--wrap=_Unwind_Resume", "role": "libraries"}, {"backtrace": 153, "fragment": "-Wl,--wrap=_Unwind_RaiseException", "role": "libraries"}, {"backtrace": 154, "fragment": "-Wl,--wrap=_Unwind_DeleteException", "role": "libraries"}, {"backtrace": 155, "fragment": "-Wl,--wrap=_Unwind_ForcedUnwind", "role": "libraries"}, {"backtrace": 156, "fragment": "-Wl,--wrap=_Unwind_Resume_or_Rethrow", "role": "libraries"}, {"backtrace": 157, "fragment": "-Wl,--wrap=_Unwind_Backtrace", "role": "libraries"}, {"backtrace": 158, "fragment": "-Wl,--wrap=__cxa_call_unexpected", "role": "libraries"}, {"backtrace": 159, "fragment": "-Wl,--wrap=__gxx_personality_v0", "role": "libraries"}, {"backtrace": 160, "fragment": "-u __cxa_guard_dummy", "role": "libraries"}, {"backtrace": 161, "fragment": "-lstdc++", "role": "libraries"}, {"backtrace": 162, "fragment": "esp-idf/pthread/libpthread.a", "role": "libraries"}, {"backtrace": 128, "fragment": "-lgcc", "role": "libraries"}, {"backtrace": 163, "fragment": "esp-idf/cxx/libcxx.a", "role": "libraries"}, {"backtrace": 164, "fragment": "-u __cxx_fatal_exception", "role": "libraries"}, {"backtrace": 166, "fragment": "-L \"/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_phy/lib/esp32\"", "role": "libraries"}, {"backtrace": 167, "fragment": "-u include_esp_phy_override", "role": "libraries"}, {"backtrace": 168, "fragment": "-lphy", "role": "libraries"}, {"backtrace": 169, "fragment": "-lrtc", "role": "libraries"}, {"backtrace": 170, "fragment": "esp-idf/esp_phy/libesp_phy.a", "role": "libraries"}, {"backtrace": 170, "fragment": "-lphy", "role": "libraries"}, {"backtrace": 170, "fragment": "-lrtc", "role": "libraries"}, {"backtrace": 170, "fragment": "esp-idf/esp_phy/libesp_phy.a", "role": "libraries"}, {"backtrace": 170, "fragment": "-lphy", "role": "libraries"}, {"backtrace": 170, "fragment": "-lrtc", "role": "libraries"}, {"backtrace": 172, "fragment": "-u vfs_include_syscalls_impl", "role": "libraries"}, {"backtrace": 173, "fragment": "-L \"/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_wifi/lib/esp32\"", "role": "libraries"}], "language": "CXX"}, "name": "libespidf.elf", "nameOnDisk": "libespidf.elf", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1]}], "sources": [{"backtrace": 2, "compileGroupIndex": 0, "isGenerated": true, "path": "build/project_elf_src_esp32.c", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/project_elf_src_esp32.c.rule", "sourceGroupIndex": 1}], "type": "EXECUTABLE"}