{"configurations": [{"directories": [{"build": ".", "childIndexes": [1], "hasInstallRule": true, "jsonFile": "directory-.-f5ebdc15457944623624.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [62, 64, 67, 69, 78, 80, 81, 82, 84, 89, 90, 96, 99, 100, 101, 102, 103]}, {"build": "esp-idf", "childIndexes": [2, 3, 4, 5, 6, 7, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 24, 25, 26, 27, 28, 30, 32, 33, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81], "hasInstallRule": true, "jsonFile": "directory-esp-idf-12602a79a01c37d87b07.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4"}, {"build": "esp-idf/xtensa", "jsonFile": "directory-esp-idf.xtensa-71a3bec848706c38a86d.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa", "targetIndexes": [60]}, {"build": "esp-idf/esp_ringbuf", "jsonFile": "directory-esp-idf.esp_ringbuf-674e08e4485de69a5a36.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_ringbuf", "targetIndexes": [26]}, {"build": "esp-idf/efuse", "jsonFile": "directory-esp-idf.efuse-510539b2006ffae33555.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse", "targetIndexes": [7, 71, 72, 73, 74, 75, 97, 98]}, {"build": "esp-idf/driver", "jsonFile": "directory-esp-idf.driver-0d5e841825ea81371284.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/driver", "targetIndexes": [6]}, {"build": "esp-idf/esp_pm", "jsonFile": "directory-esp-idf.esp_pm-43516d3a54347449b9ec.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_pm", "targetIndexes": [25]}, {"build": "esp-idf/mbedtls", "childIndexes": [8], "hasInstallRule": true, "jsonFile": "directory-esp-idf.mbedtls-1d7297a548aa7707712f.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/mbedtls", "targetIndexes": [41, 70]}, {"build": "esp-idf/mbedtls/mbedtls", "childIndexes": [9, 10, 11], "hasInstallRule": true, "jsonFile": "directory-esp-idf.mbedtls.mbedtls-3fe291a22cc70db4f0ee.json", "minimumCMakeVersion": {"string": "3.5.1"}, "parentIndex": 7, "projectIndex": 2, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/mbedtls/mbedtls", "targetIndexes": [63]}, {"build": "esp-idf/mbedtls/mbedtls/include", "hasInstallRule": true, "jsonFile": "directory-esp-idf.mbedtls.mbedtls.include-60815da710eada0f4954.json", "minimumCMakeVersion": {"string": "3.5.1"}, "parentIndex": 8, "projectIndex": 2, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/mbedtls/mbedtls/include"}, {"build": "esp-idf/mbedtls/mbedtls/3rdparty", "jsonFile": "directory-esp-idf.mbedtls.mbedtls.3rdparty-955ad001090bf9a35d78.json", "minimumCMakeVersion": {"string": "3.5.1"}, "parentIndex": 8, "projectIndex": 2, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/mbedtls/mbedtls/3rdparty"}, {"build": "esp-idf/mbedtls/mbedtls/library", "hasInstallRule": true, "jsonFile": "directory-esp-idf.mbedtls.mbedtls.library-f009ab41da91ec982ec4.json", "minimumCMakeVersion": {"string": "3.5.1"}, "parentIndex": 8, "projectIndex": 2, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/mbedtls/mbedtls/library", "targetIndexes": [83, 85, 86, 87]}, {"build": "esp-idf/esp_app_format", "jsonFile": "directory-esp-idf.esp_app_format-2693eda0723af14c9e54.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_app_format", "targetIndexes": [10]}, {"build": "esp-idf/bootloader_support", "jsonFile": "directory-esp-idf.bootloader_support-1f00c7ba2c222071200f.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support", "targetIndexes": [2]}, {"build": "esp-idf/bootloader", "jsonFile": "directory-esp-idf.bootloader-f60a4c82c87f5334166e.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader", "targetIndexes": [68, 77]}, {"build": "esp-idf/esptool_py", "jsonFile": "directory-esp-idf.esptool_py-238f1fd253b18dcddce8.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esptool_py", "targetIndexes": [65, 66, 76]}, {"build": "esp-idf/partition_table", "jsonFile": "directory-esp-idf.partition_table-f89358135db232f4040d.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/partition_table", "targetIndexes": [79, 91, 92, 93, 94, 95]}, {"build": "esp-idf/esp_partition", "jsonFile": "directory-esp-idf.esp_partition-f6f2f0470e06904e46ef.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_partition", "targetIndexes": [23]}, {"build": "esp-idf/app_update", "jsonFile": "directory-esp-idf.app_update-328943ccfefad1e26a91.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/app_update", "targetIndexes": [1]}, {"build": "esp-idf/spi_flash", "jsonFile": "directory-esp-idf.spi_flash-8525a93240472d793a3f.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/spi_flash", "targetIndexes": [51]}, {"build": "esp-idf/pthread", "jsonFile": "directory-esp-idf.pthread-22c89c8160ab0c5a0a5c.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/pthread", "targetIndexes": [48]}, {"build": "esp-idf/esp_system", "childIndexes": [22], "jsonFile": "directory-esp-idf.esp_system-57989546e983b76c6dec.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_system", "targetIndexes": [28, 61, 88]}, {"build": "esp-idf/esp_system/port", "childIndexes": [23], "jsonFile": "directory-esp-idf.esp_system.port-c4a6c631bdb563ff7bbb.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 21, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_system/port"}, {"build": "esp-idf/esp_system/port/soc/esp32", "jsonFile": "directory-esp-idf.esp_system.port.soc.esp32-0742d5574cf08c3194d9.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 22, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_system/port/soc/esp32"}, {"build": "esp-idf/esp_rom", "jsonFile": "directory-esp-idf.esp_rom-a1af55acd3f1139a85a6.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom", "targetIndexes": [27]}, {"build": "esp-idf/hal", "jsonFile": "directory-esp-idf.hal-c4dea1c3918036177551.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal", "targetIndexes": [34]}, {"build": "esp-idf/log", "jsonFile": "directory-esp-idf.log-eb8bd0411971119feddd.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log", "targetIndexes": [38]}, {"build": "esp-idf/heap", "jsonFile": "directory-esp-idf.heap-5fefdd511198e280e623.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/heap", "targetIndexes": [35]}, {"build": "esp-idf/soc", "childIndexes": [29], "jsonFile": "directory-esp-idf.soc-2c5dedea29a4df9baf80.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc", "targetIndexes": [50]}, {"build": "esp-idf/soc/esp32", "jsonFile": "directory-esp-idf.soc.esp32-ad5f11fe7754a7ab4466.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 28, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32"}, {"build": "esp-idf/esp_hw_support", "childIndexes": [31], "jsonFile": "directory-esp-idf.esp_hw_support-a6a7a1d15e02213ab091.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support", "targetIndexes": [19]}, {"build": "esp-idf/esp_hw_support/port/esp32", "jsonFile": "directory-esp-idf.esp_hw_support.port.esp32-fb644556c822f649137f.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 30, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32"}, {"build": "esp-idf/freertos", "jsonFile": "directory-esp-idf.freertos-c25da768c57d0a5acd2a.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/freertos", "targetIndexes": [33]}, {"build": "esp-idf/newlib", "childIndexes": [34], "jsonFile": "directory-esp-idf.newlib-498c3df5e0f4ffcf1b80.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib", "targetIndexes": [43]}, {"build": "esp-idf/newlib/port", "jsonFile": "directory-esp-idf.newlib.port-ba4567ad81a178df1fa0.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 33, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/port"}, {"build": "esp-idf/cxx", "jsonFile": "directory-esp-idf.cxx-d23e4f8fba39cfc5e77d.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/cxx", "targetIndexes": [5]}, {"build": "esp-idf/esp_common", "jsonFile": "directory-esp-idf.esp_common-649510eee07e10a73b1e.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common", "targetIndexes": [11]}, {"build": "esp-idf/esp_timer", "jsonFile": "directory-esp-idf.esp_timer-d7551ececc12784e032b.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_timer", "targetIndexes": [29]}, {"build": "esp-idf/app_trace", "jsonFile": "directory-esp-idf.app_trace-d14c6e5645bb2d8507ef.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/app_trace", "targetIndexes": [0]}, {"build": "esp-idf/esp_event", "jsonFile": "directory-esp-idf.esp_event-4c007e90187352a6d344.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_event", "targetIndexes": [13]}, {"build": "esp-idf/nvs_flash", "jsonFile": "directory-esp-idf.nvs_flash-64e02170184fe1b8f3e1.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/nvs_flash", "targetIndexes": [44]}, {"build": "esp-idf/esp_phy", "jsonFile": "directory-esp-idf.esp_phy-014fa22cf72d5c343747.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_phy", "targetIndexes": [24]}, {"build": "esp-idf/vfs", "jsonFile": "directory-esp-idf.vfs-8d59246b557729c3c756.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/vfs", "targetIndexes": [56]}, {"build": "esp-idf/lwip", "jsonFile": "directory-esp-idf.lwip-904741a52ea681eb3823.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/lwip", "targetIndexes": [39]}, {"build": "esp-idf/esp_netif", "jsonFile": "directory-esp-idf.esp_netif-d3ec1fad89f26f4a68f2.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_netif", "targetIndexes": [22]}, {"build": "esp-idf/wpa_supplicant", "jsonFile": "directory-esp-idf.wpa_supplicant-697f6bef74a4242b7074.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/wpa_supplicant", "targetIndexes": [59]}, {"build": "esp-idf/esp_wifi", "jsonFile": "directory-esp-idf.esp_wifi-12872d5cdccb93ff00ac.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_wifi", "targetIndexes": [30]}, {"build": "esp-idf/bt", "jsonFile": "directory-esp-idf.bt-584c49c80d22daf4852d.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bt"}, {"build": "esp-idf/unity", "jsonFile": "directory-esp-idf.unity-00fbc9fdbf315d96a811.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/unity", "targetIndexes": [55]}, {"build": "esp-idf/cmock", "jsonFile": "directory-esp-idf.cmock-dbff1c33bc4fab0b9e4d.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/cmock", "targetIndexes": [3]}, {"build": "esp-idf/console", "jsonFile": "directory-esp-idf.console-43352b1530a6e38ccd85.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/console", "targetIndexes": [4]}, {"build": "esp-idf/http_parser", "jsonFile": "directory-esp-idf.http_parser-92e980766df7136cd276.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/http_parser", "targetIndexes": [36]}, {"build": "esp-idf/esp-tls", "jsonFile": "directory-esp-idf.esp-tls-00b2e563254252163593.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp-tls", "targetIndexes": [8]}, {"build": "esp-idf/esp_adc", "jsonFile": "directory-esp-idf.esp_adc-0f51e79375e2388080ce.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_adc", "targetIndexes": [9]}, {"build": "esp-idf/esp_eth", "jsonFile": "directory-esp-idf.esp_eth-12c6b8346104f36214e0.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_eth", "targetIndexes": [12]}, {"build": "esp-idf/esp_gdbstub", "jsonFile": "directory-esp-idf.esp_gdbstub-cf3ed93c8396d0708318.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_gdbstub", "targetIndexes": [14]}, {"build": "esp-idf/esp_hid", "jsonFile": "directory-esp-idf.esp_hid-6c19ab0c81bdb3cc78c2.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hid", "targetIndexes": [15]}, {"build": "esp-idf/tcp_transport", "jsonFile": "directory-esp-idf.tcp_transport-faccc9dc5d4be3a53be1.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/tcp_transport", "targetIndexes": [53]}, {"build": "esp-idf/esp_http_client", "jsonFile": "directory-esp-idf.esp_http_client-d4b1408c53c0389f3d68.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_http_client", "targetIndexes": [16]}, {"build": "esp-idf/esp_http_server", "jsonFile": "directory-esp-idf.esp_http_server-9dfd73f67c491c3277fd.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_http_server", "targetIndexes": [17]}, {"build": "esp-idf/esp_https_ota", "jsonFile": "directory-esp-idf.esp_https_ota-cd7eaa8ff68b5f3eb137.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_https_ota", "targetIndexes": [18]}, {"build": "esp-idf/esp_https_server", "jsonFile": "directory-esp-idf.esp_https_server-3272a1dbfc316dc0a6d0.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_https_server"}, {"build": "esp-idf/esp_lcd", "jsonFile": "directory-esp-idf.esp_lcd-2eebc929bd9afb2924e7.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_lcd", "targetIndexes": [20]}, {"build": "esp-idf/protobuf-c", "jsonFile": "directory-esp-idf.protobuf-c-a455048788c0b70e7380.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/protobuf-c", "targetIndexes": [46]}, {"build": "esp-idf/protocomm", "jsonFile": "directory-esp-idf.protocomm-bcb649557ae3fa7b882e.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/protocomm", "targetIndexes": [47]}, {"build": "esp-idf/esp_local_ctrl", "jsonFile": "directory-esp-idf.esp_local_ctrl-57f124fa41fcaf66dfe7.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_local_ctrl", "targetIndexes": [21]}, {"build": "esp-idf/esp_psram", "jsonFile": "directory-esp-idf.esp_psram-5cf273182edb093197e9.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_psram"}, {"build": "esp-idf/espcoredump", "jsonFile": "directory-esp-idf.espcoredump-3eb8ec7bcd3d44d452aa.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/espcoredump", "targetIndexes": [31]}, {"build": "esp-idf/wear_levelling", "jsonFile": "directory-esp-idf.wear_levelling-d2990a21a148ffa0b12c.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/wear_levelling", "targetIndexes": [57]}, {"build": "esp-idf/sdmmc", "jsonFile": "directory-esp-idf.sdmmc-c489a32c3beae046654f.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/sdmmc", "targetIndexes": [49]}, {"build": "esp-idf/fatfs", "jsonFile": "directory-esp-idf.fatfs-5417b8183af3121ba049.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/fatfs", "targetIndexes": [32]}, {"build": "esp-idf/idf_test", "jsonFile": "directory-esp-idf.idf_test-320f4cdb0cedca0c5165.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/idf_test"}, {"build": "esp-idf/ieee802154", "jsonFile": "directory-esp-idf.ieee802154-c6d74f66eff2f43975f1.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/ieee802154"}, {"build": "esp-idf/json", "jsonFile": "directory-esp-idf.json-ccd3419074928909cb89.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/json", "targetIndexes": [37]}, {"build": "esp-idf/mqtt", "jsonFile": "directory-esp-idf.mqtt-a4dd809358d1a359e461.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/mqtt", "targetIndexes": [42]}, {"build": "esp-idf/openthread", "jsonFile": "directory-esp-idf.openthread-412cfc4f74dd7daacba0.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/openthread"}, {"build": "esp-idf/perfmon", "jsonFile": "directory-esp-idf.perfmon-1b269b6b3ee58fc730de.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/perfmon", "targetIndexes": [45]}, {"build": "esp-idf/spiffs", "jsonFile": "directory-esp-idf.spiffs-e8c9499a82bc48235605.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/spiffs", "targetIndexes": [52]}, {"build": "esp-idf/ulp", "jsonFile": "directory-esp-idf.ulp-6cc27e7de7ef609fa0b7.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/ulp", "targetIndexes": [54]}, {"build": "esp-idf/usb", "jsonFile": "directory-esp-idf.usb-a3d4a924d37d02224e9c.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/usb"}, {"build": "esp-idf/wifi_provisioning", "jsonFile": "directory-esp-idf.wifi_provisioning-ab45cad3ca9e6921ffd7.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/wifi_provisioning", "targetIndexes": [58]}, {"build": "esp-idf/main", "jsonFile": "directory-esp-idf.main-f27928b5f5a70a8f4668.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 1, "source": "main", "targetIndexes": [40]}], "name": "", "projects": [{"childIndexes": [1], "directoryIndexes": [0], "name": "libes<PERSON><PERSON>", "targetIndexes": [62, 64, 67, 69, 78, 80, 81, 82, 84, 89, 90, 96, 99, 100, 101, 102, 103]}, {"childIndexes": [2], "directoryIndexes": [1, 2, 3, 4, 5, 6, 7, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81], "name": "esp-idf", "parentIndex": 0, "targetIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 65, 66, 68, 70, 71, 72, 73, 74, 75, 76, 77, 79, 88, 91, 92, 93, 94, 95, 97, 98]}, {"directoryIndexes": [8, 9, 10, 11], "name": "mbed TLS", "parentIndex": 1, "targetIndexes": [63, 83, 85, 86, 87]}], "targets": [{"directoryIndex": 38, "id": "__idf_app_trace::@dd7eb3aa23293586d3b8", "jsonFile": "target-__idf_app_trace-cecd1ee69918bbc194a0.json", "name": "__idf_app_trace", "projectIndex": 1}, {"directoryIndex": 18, "id": "__idf_app_update::@cc3b426666063acc9674", "jsonFile": "target-__idf_app_update-04c12351d62caae2c1b4.json", "name": "__idf_app_update", "projectIndex": 1}, {"directoryIndex": 13, "id": "__idf_bootloader_support::@d88583f91142925c6676", "jsonFile": "target-__idf_bootloader_support-fc3483e1400bd975628a.json", "name": "__idf_bootloader_support", "projectIndex": 1}, {"directoryIndex": 49, "id": "__idf_cmock::@38f9eb59693b7394f54c", "jsonFile": "target-__idf_cmock-31b5ab7e94dd41f879ff.json", "name": "__idf_cmock", "projectIndex": 1}, {"directoryIndex": 50, "id": "__idf_console::@4311ed8d271cc0ee1422", "jsonFile": "target-__idf_console-eb8094444ea8da6937ed.json", "name": "__idf_console", "projectIndex": 1}, {"directoryIndex": 35, "id": "__idf_cxx::@fc68b7334ac9a9392d41", "jsonFile": "target-__idf_cxx-d6ed5a22c3ffbf0458e4.json", "name": "__idf_cxx", "projectIndex": 1}, {"directoryIndex": 5, "id": "__idf_driver::@76403182dd99a02c203e", "jsonFile": "target-__idf_driver-93b8b8639abfa5b33886.json", "name": "__idf_driver", "projectIndex": 1}, {"directoryIndex": 4, "id": "__idf_efuse::@2da764c922f8b73e9c75", "jsonFile": "target-__idf_efuse-204cda0adcdcaff6d777.json", "name": "__idf_efuse", "projectIndex": 1}, {"directoryIndex": 52, "id": "__idf_esp-tls::@fec40229f530785b7d3e", "jsonFile": "target-__idf_esp-tls-b79533350537162b7a28.json", "name": "__idf_esp-tls", "projectIndex": 1}, {"directoryIndex": 53, "id": "__idf_esp_adc::@847b8318c687ffb50ebb", "jsonFile": "target-__idf_esp_adc-fe1f655b2de2572ce8d4.json", "name": "__idf_esp_adc", "projectIndex": 1}, {"directoryIndex": 12, "id": "__idf_esp_app_format::@08c2bdce7fd85cdbea06", "jsonFile": "target-__idf_esp_app_format-9d10a853f0bd145ca27f.json", "name": "__idf_esp_app_format", "projectIndex": 1}, {"directoryIndex": 36, "id": "__idf_esp_common::@5ba0aac035ae9b4e20a4", "jsonFile": "target-__idf_esp_common-90b1f1c6302e819a9c24.json", "name": "__idf_esp_common", "projectIndex": 1}, {"directoryIndex": 54, "id": "__idf_esp_eth::@68140d310044213ed0bc", "jsonFile": "target-__idf_esp_eth-23e783e0f62a558effd8.json", "name": "__idf_esp_eth", "projectIndex": 1}, {"directoryIndex": 39, "id": "__idf_esp_event::@1426f3cbfb4523713151", "jsonFile": "target-__idf_esp_event-42b8b0532a2a4b8795b5.json", "name": "__idf_esp_event", "projectIndex": 1}, {"directoryIndex": 55, "id": "__idf_esp_gdbstub::@377a8c6ee3088554e323", "jsonFile": "target-__idf_esp_gdbstub-ae1ea367f1589a4bccb6.json", "name": "__idf_esp_gdbstub", "projectIndex": 1}, {"directoryIndex": 56, "id": "__idf_esp_hid::@15df40e16a5d8775dd9b", "jsonFile": "target-__idf_esp_hid-013bf77e9e3cb6f43f4d.json", "name": "__idf_esp_hid", "projectIndex": 1}, {"directoryIndex": 58, "id": "__idf_esp_http_client::@483ffd9ca1a06508c93f", "jsonFile": "target-__idf_esp_http_client-f7b3bc9d0546202c6fec.json", "name": "__idf_esp_http_client", "projectIndex": 1}, {"directoryIndex": 59, "id": "__idf_esp_http_server::@1e8eede8f41363800887", "jsonFile": "target-__idf_esp_http_server-4a5f845644cad2b3ef32.json", "name": "__idf_esp_http_server", "projectIndex": 1}, {"directoryIndex": 60, "id": "__idf_esp_https_ota::@55ee5ec07370a5473c2b", "jsonFile": "target-__idf_esp_https_ota-9cf8bf7b239b0337eb4d.json", "name": "__idf_esp_https_ota", "projectIndex": 1}, {"directoryIndex": 30, "id": "__idf_esp_hw_support::@ce4791f5a7554c569ddb", "jsonFile": "target-__idf_esp_hw_support-fbe399477cfb05e1b08e.json", "name": "__idf_esp_hw_support", "projectIndex": 1}, {"directoryIndex": 62, "id": "__idf_esp_lcd::@d5a84597e1633c9ab0d3", "jsonFile": "target-__idf_esp_lcd-2786e3bf30fda1798072.json", "name": "__idf_esp_lcd", "projectIndex": 1}, {"directoryIndex": 65, "id": "__idf_esp_local_ctrl::@d6aad58d2946e56208ef", "jsonFile": "target-__idf_esp_local_ctrl-b205a89c948514d41be1.json", "name": "__idf_esp_local_ctrl", "projectIndex": 1}, {"directoryIndex": 44, "id": "__idf_esp_netif::@0dd2a33d00052a11574d", "jsonFile": "target-__idf_esp_netif-0748346c2ec6265ec484.json", "name": "__idf_esp_netif", "projectIndex": 1}, {"directoryIndex": 17, "id": "__idf_esp_partition::@d7cc29bd06eede980e19", "jsonFile": "target-__idf_esp_partition-2c7f74e9f844f1ed39a4.json", "name": "__idf_esp_partition", "projectIndex": 1}, {"directoryIndex": 41, "id": "__idf_esp_phy::@c4472d8d68a6cb344214", "jsonFile": "target-__idf_esp_phy-d4c350355c543a193f96.json", "name": "__idf_esp_phy", "projectIndex": 1}, {"directoryIndex": 6, "id": "__idf_esp_pm::@c93acdd4f72df5fa58db", "jsonFile": "target-__idf_esp_pm-a7937a55391b55c3d29f.json", "name": "__idf_esp_pm", "projectIndex": 1}, {"directoryIndex": 3, "id": "__idf_esp_ringbuf::@ad62994b1ce98b81017d", "jsonFile": "target-__idf_esp_ringbuf-8a7c123222aed9e4ba27.json", "name": "__idf_esp_ringbuf", "projectIndex": 1}, {"directoryIndex": 24, "id": "__idf_esp_rom::@c269e361cd87ad81e927", "jsonFile": "target-__idf_esp_rom-25be64eb19c7d33e0045.json", "name": "__idf_esp_rom", "projectIndex": 1}, {"directoryIndex": 21, "id": "__idf_esp_system::@874df54e61e20112dfd4", "jsonFile": "target-__idf_esp_system-ff4b71d3fc72e8a753f3.json", "name": "__idf_esp_system", "projectIndex": 1}, {"directoryIndex": 37, "id": "__idf_esp_timer::@08b57a33b2000f8ac4ee", "jsonFile": "target-__idf_esp_timer-1aa185fb09b93bbc0c06.json", "name": "__idf_esp_timer", "projectIndex": 1}, {"directoryIndex": 46, "id": "__idf_esp_wifi::@c33f1523799863800685", "jsonFile": "target-__idf_esp_wifi-ef3cd06d0beeb9bb30a3.json", "name": "__idf_esp_wifi", "projectIndex": 1}, {"directoryIndex": 67, "id": "__idf_espcoredump::@718b9a7d55ab42cfc0a3", "jsonFile": "target-__idf_espcoredump-1c9ffc63d84901172129.json", "name": "__idf_espcoredump", "projectIndex": 1}, {"directoryIndex": 70, "id": "__idf_fatfs::@75ac1d9b25dacc8cc9e7", "jsonFile": "target-__idf_fatfs-4b04769e700a41cb209f.json", "name": "__idf_fatfs", "projectIndex": 1}, {"directoryIndex": 32, "id": "__idf_freertos::@7334568587001871092c", "jsonFile": "target-__idf_freertos-99d2907bdfc026ef65f5.json", "name": "__idf_freertos", "projectIndex": 1}, {"directoryIndex": 25, "id": "__idf_hal::@41c1a00f56cd1e9ede18", "jsonFile": "target-__idf_hal-246e95ce8eb7f7f886ba.json", "name": "__idf_hal", "projectIndex": 1}, {"directoryIndex": 27, "id": "__idf_heap::@a3bf99b7c47be461ad9f", "jsonFile": "target-__idf_heap-0c9e2aa1c51041239b8b.json", "name": "__idf_heap", "projectIndex": 1}, {"directoryIndex": 51, "id": "__idf_http_parser::@358cb9c2b49fd348008f", "jsonFile": "target-__idf_http_parser-9ca29131f11b64dc2fce.json", "name": "__idf_http_parser", "projectIndex": 1}, {"directoryIndex": 73, "id": "__idf_json::@c78a8bdc820334f01148", "jsonFile": "target-__idf_json-75130e0e98f04b295aa1.json", "name": "__idf_json", "projectIndex": 1}, {"directoryIndex": 26, "id": "__idf_log::@d5e1aa0ef4e3c98c8a70", "jsonFile": "target-__idf_log-d9b110e1f60f289eba5f.json", "name": "__idf_log", "projectIndex": 1}, {"directoryIndex": 43, "id": "__idf_lwip::@72bd17b7eb336521f94d", "jsonFile": "target-__idf_lwip-d1173a469e7f2884dd54.json", "name": "__idf_lwip", "projectIndex": 1}, {"directoryIndex": 81, "id": "__idf_main::@7368607ed66887415643", "jsonFile": "target-__idf_main-07bbbaa34e6ce05e8e40.json", "name": "__idf_main", "projectIndex": 1}, {"directoryIndex": 7, "id": "__idf_mbedtls::@9d0b8d44c897d31e7973", "jsonFile": "target-__idf_mbedtls-b31583b0468e8fd21e3f.json", "name": "__idf_mbedtls", "projectIndex": 1}, {"directoryIndex": 74, "id": "__idf_mqtt::@c1ab2c6a99226d1f4b56", "jsonFile": "target-__idf_mqtt-7f1808a18047f9a39a1d.json", "name": "__idf_mqtt", "projectIndex": 1}, {"directoryIndex": 33, "id": "__idf_newlib::@1dbc38583ffafd67f967", "jsonFile": "target-__idf_newlib-1bd972a8970fd966a57f.json", "name": "__idf_newlib", "projectIndex": 1}, {"directoryIndex": 40, "id": "__idf_nvs_flash::@b6cf96db3b320cf2c923", "jsonFile": "target-__idf_nvs_flash-7e4b2c5e6d8b6622ab08.json", "name": "__idf_nvs_flash", "projectIndex": 1}, {"directoryIndex": 76, "id": "__idf_perfmon::@aac0f647c32bb8f54a67", "jsonFile": "target-__idf_perfmon-50068da956ee918fb4e9.json", "name": "__idf_perfmon", "projectIndex": 1}, {"directoryIndex": 63, "id": "__idf_protobuf-c::@b14513c4bd859268ec22", "jsonFile": "target-__idf_protobuf-c-b0da6c4bb78ee68427b2.json", "name": "__idf_protobuf-c", "projectIndex": 1}, {"directoryIndex": 64, "id": "__idf_protocomm::@d76d01c8dec06205e1fc", "jsonFile": "target-__idf_protocomm-38eafa6cd9385932c898.json", "name": "__idf_protocomm", "projectIndex": 1}, {"directoryIndex": 20, "id": "__idf_pthread::@52b2aa0be8fa4ca9e52e", "jsonFile": "target-__idf_pthread-5d930f113acf9bd5ba38.json", "name": "__idf_pthread", "projectIndex": 1}, {"directoryIndex": 69, "id": "__idf_sdmmc::@89ed5bca1750ffbc7db6", "jsonFile": "target-__idf_sdmmc-e3cf144ea1a4212fa65c.json", "name": "__idf_sdmmc", "projectIndex": 1}, {"directoryIndex": 28, "id": "__idf_soc::@824f1541829f13857909", "jsonFile": "target-__idf_soc-ab2bbdaa6bb9a78f4860.json", "name": "__idf_soc", "projectIndex": 1}, {"directoryIndex": 19, "id": "__idf_spi_flash::@6f8a8c72478269b2cd60", "jsonFile": "target-__idf_spi_flash-50ea11440acbaf058327.json", "name": "__idf_spi_flash", "projectIndex": 1}, {"directoryIndex": 77, "id": "__idf_spiffs::@df881b71474064c86689", "jsonFile": "target-__idf_spiffs-7cf0928cc22b1029324b.json", "name": "__idf_spiffs", "projectIndex": 1}, {"directoryIndex": 57, "id": "__idf_tcp_transport::@2217ed266b3ca9834900", "jsonFile": "target-__idf_tcp_transport-06b9ee182d5659fc9764.json", "name": "__idf_tcp_transport", "projectIndex": 1}, {"directoryIndex": 78, "id": "__idf_ulp::@59f74c71af7e30dd3e55", "jsonFile": "target-__idf_ulp-d72e05c07681d0e34834.json", "name": "__idf_ulp", "projectIndex": 1}, {"directoryIndex": 48, "id": "__idf_unity::@fec746937506b5690992", "jsonFile": "target-__idf_unity-d4c8f1bbc7027357adb4.json", "name": "__idf_unity", "projectIndex": 1}, {"directoryIndex": 42, "id": "__idf_vfs::@57481f90220bce4f7e1a", "jsonFile": "target-__idf_vfs-330e56c14f2c14d97132.json", "name": "__idf_vfs", "projectIndex": 1}, {"directoryIndex": 68, "id": "__idf_wear_levelling::@cb21b0d7c0815cff11c6", "jsonFile": "target-__idf_wear_levelling-e9f50443f9e1456ca826.json", "name": "__idf_wear_levelling", "projectIndex": 1}, {"directoryIndex": 80, "id": "__idf_wifi_provisioning::@9d7e7ffe1b15746c1098", "jsonFile": "target-__idf_wifi_provisioning-e1258c8e3c2e922359a3.json", "name": "__idf_wifi_provisioning", "projectIndex": 1}, {"directoryIndex": 45, "id": "__idf_wpa_supplicant::@2124e86f93fbab1c8043", "jsonFile": "target-__idf_wpa_supplicant-8c7a600ad482ff926665.json", "name": "__idf_wpa_supplicant", "projectIndex": 1}, {"directoryIndex": 2, "id": "__idf_xtensa::@1daaaad7abe597e030f9", "jsonFile": "target-__idf_xtensa-acd72186ddef21fffa87.json", "name": "__idf_xtensa", "projectIndex": 1}, {"directoryIndex": 21, "id": "__ldgen_output_sections.ld::@874df54e61e20112dfd4", "jsonFile": "target-__ldgen_output_sections.ld-421acbf00465c7082905.json", "name": "__ldgen_output_sections.ld", "projectIndex": 1}, {"directoryIndex": 0, "id": "_project_elf_src::@6890427a1f51a3e7e1df", "jsonFile": "target-_project_elf_src-b057da17ae40cefab52f.json", "name": "_project_elf_src", "projectIndex": 0}, {"directoryIndex": 8, "id": "apidoc::@b8f87a1a94d8d9bb59bb", "jsonFile": "target-apidoc-9b49a1ab461dbd43c9c1.json", "name": "apidoc", "projectIndex": 2}, {"directoryIndex": 0, "id": "app::@6890427a1f51a3e7e1df", "jsonFile": "target-app-bcebc43bd0c99a276ad9.json", "name": "app", "projectIndex": 0}, {"directoryIndex": 15, "id": "app-flash::@bf8f55be585a12323665", "jsonFile": "target-app-flash-051f3ec901574f8a463f.json", "name": "app-flash", "projectIndex": 1}, {"directoryIndex": 15, "id": "app_check_size::@bf8f55be585a12323665", "jsonFile": "target-app_check_size-f26f7aa1c4fb47a49f8e.json", "name": "app_check_size", "projectIndex": 1}, {"directoryIndex": 0, "id": "bootloader::@6890427a1f51a3e7e1df", "jsonFile": "target-bootloader-19cd2912024651e3c765.json", "name": "bootloader", "projectIndex": 0}, {"directoryIndex": 14, "id": "bootloader-flash::@4013c5adf9c02e1a402c", "jsonFile": "target-bootloader-flash-58272ab73091097d3b0e.json", "name": "bootloader-flash", "projectIndex": 1}, {"directoryIndex": 0, "id": "confserver::@6890427a1f51a3e7e1df", "jsonFile": "target-confserver-5a97653d2114d516203d.json", "name": "confserver", "projectIndex": 0}, {"directoryIndex": 7, "id": "custom_bundle::@9d0b8d44c897d31e7973", "jsonFile": "target-custom_bundle-3c835b61722b9983fa5f.json", "name": "custom_bundle", "projectIndex": 1}, {"directoryIndex": 4, "id": "efuse-common-table::@2da764c922f8b73e9c75", "jsonFile": "target-efuse-common-table-1a377090f1ef8e21d6fc.json", "name": "efuse-common-table", "projectIndex": 1}, {"directoryIndex": 4, "id": "efuse-custom-table::@2da764c922f8b73e9c75", "jsonFile": "target-efuse-custom-table-afe7315e233ebb31686c.json", "name": "efuse-custom-table", "projectIndex": 1}, {"directoryIndex": 4, "id": "efuse_common_table::@2da764c922f8b73e9c75", "jsonFile": "target-efuse_common_table-aae293112efd41875c22.json", "name": "efuse_common_table", "projectIndex": 1}, {"directoryIndex": 4, "id": "efuse_custom_table::@2da764c922f8b73e9c75", "jsonFile": "target-efuse_custom_table-a9411f7ddf34d6806e25.json", "name": "efuse_custom_table", "projectIndex": 1}, {"directoryIndex": 4, "id": "efuse_test_table::@2da764c922f8b73e9c75", "jsonFile": "target-efuse_test_table-4ca9b1eacc421661f2e5.json", "name": "efuse_test_table", "projectIndex": 1}, {"directoryIndex": 15, "id": "encrypted-app-flash::@bf8f55be585a12323665", "jsonFile": "target-encrypted-app-flash-b5d761311dd4854eea0f.json", "name": "encrypted-app-flash", "projectIndex": 1}, {"directoryIndex": 14, "id": "encrypted-bootloader-flash::@4013c5adf9c02e1a402c", "jsonFile": "target-encrypted-bootloader-flash-91dca4e6e1cea4eafaff.json", "name": "encrypted-bootloader-flash", "projectIndex": 1}, {"directoryIndex": 0, "id": "encrypted-flash::@6890427a1f51a3e7e1df", "jsonFile": "target-encrypted-flash-b8025c7634b681788871.json", "name": "encrypted-flash", "projectIndex": 0}, {"directoryIndex": 16, "id": "encrypted-partition-table-flash::@8e7a60ee4fe55e1aaabf", "jsonFile": "target-encrypted-partition-table-flash-9d2e88ffaa2c781a1134.json", "name": "encrypted-partition-table-flash", "projectIndex": 1}, {"directoryIndex": 0, "id": "erase_flash::@6890427a1f51a3e7e1df", "jsonFile": "target-erase_flash-3049d4efeb6cb238b10f.json", "name": "erase_flash", "projectIndex": 0}, {"directoryIndex": 0, "id": "flash::@6890427a1f51a3e7e1df", "jsonFile": "target-flash-2ed8966682daf48eb34e.json", "name": "flash", "projectIndex": 0}, {"directoryIndex": 0, "id": "gen_project_binary::@6890427a1f51a3e7e1df", "jsonFile": "target-gen_project_binary-18185971b74cf8a21442.json", "name": "gen_project_binary", "projectIndex": 0}, {"directoryIndex": 11, "id": "lib::@12759e2a35724d846b97", "jsonFile": "target-lib-c25b49cc4ab00c02a484.json", "name": "lib", "projectIndex": 2}, {"directoryIndex": 0, "id": "libespidf.elf::@6890427a1f51a3e7e1df", "jsonFile": "target-libespidf.elf-758839fe12b1510d8b25.json", "name": "libespidf.elf", "projectIndex": 0}, {"directoryIndex": 11, "id": "mbedcrypto::@12759e2a35724d846b97", "jsonFile": "target-mbedcrypto-f5daf02730d64011d479.json", "name": "mbedcrypto", "projectIndex": 2}, {"directoryIndex": 11, "id": "mbedtls::@12759e2a35724d846b97", "jsonFile": "target-mbedtls-793ffb4c678f6d591ff8.json", "name": "mbedtls", "projectIndex": 2}, {"directoryIndex": 11, "id": "mbedx509::@12759e2a35724d846b97", "jsonFile": "target-mbedx509-c9d1296bf2548acda974.json", "name": "mbedx509", "projectIndex": 2}, {"directoryIndex": 21, "id": "memory_ld::@874df54e61e20112dfd4", "jsonFile": "target-memory_ld-08754294ac8c478d0d7f.json", "name": "memory_ld", "projectIndex": 1}, {"directoryIndex": 0, "id": "menuconfig::@6890427a1f51a3e7e1df", "jsonFile": "target-menuconfig-10776b1d3e07e9bb72f8.json", "name": "menuconfig", "projectIndex": 0}, {"directoryIndex": 0, "id": "monitor::@6890427a1f51a3e7e1df", "jsonFile": "target-monitor-161602ccb8476be780e1.json", "name": "monitor", "projectIndex": 0}, {"directoryIndex": 16, "id": "partition-table::@8e7a60ee4fe55e1aaabf", "jsonFile": "target-partition-table-913a84eb2e881587254e.json", "name": "partition-table", "projectIndex": 1}, {"directoryIndex": 16, "id": "partition-table-flash::@8e7a60ee4fe55e1aaabf", "jsonFile": "target-partition-table-flash-d614565506f02ab6a3c9.json", "name": "partition-table-flash", "projectIndex": 1}, {"directoryIndex": 16, "id": "partition_table::@8e7a60ee4fe55e1aaabf", "jsonFile": "target-partition_table-ed9937338a1a049f8e2f.json", "name": "partition_table", "projectIndex": 1}, {"directoryIndex": 16, "id": "partition_table-flash::@8e7a60ee4fe55e1aaabf", "jsonFile": "target-partition_table-flash-fefeeb99004622f35adb.json", "name": "partition_table-flash", "projectIndex": 1}, {"directoryIndex": 16, "id": "partition_table_bin::@8e7a60ee4fe55e1aaabf", "jsonFile": "target-partition_table_bin-46e65bb941ed727a9316.json", "name": "partition_table_bin", "projectIndex": 1}, {"directoryIndex": 0, "id": "save-defconfig::@6890427a1f51a3e7e1df", "jsonFile": "target-save-defconfig-cf8cee967af7ee754899.json", "name": "save-defconfig", "projectIndex": 0}, {"directoryIndex": 4, "id": "show-efuse-table::@2da764c922f8b73e9c75", "jsonFile": "target-show-efuse-table-ef7963744bd95c48e239.json", "name": "show-efuse-table", "projectIndex": 1}, {"directoryIndex": 4, "id": "show_efuse_table::@2da764c922f8b73e9c75", "jsonFile": "target-show_efuse_table-b428634467b539626985.json", "name": "show_efuse_table", "projectIndex": 1}, {"directoryIndex": 0, "id": "size::@6890427a1f51a3e7e1df", "jsonFile": "target-size-60f65437622682a1322c.json", "name": "size", "projectIndex": 0}, {"directoryIndex": 0, "id": "size-components::@6890427a1f51a3e7e1df", "jsonFile": "target-size-components-63ab858831f88eb25729.json", "name": "size-components", "projectIndex": 0}, {"directoryIndex": 0, "id": "size-files::@6890427a1f51a3e7e1df", "jsonFile": "target-size-files-f58a5b7ea06ad5fcc678.json", "name": "size-files", "projectIndex": 0}, {"directoryIndex": 0, "id": "uf2::@6890427a1f51a3e7e1df", "jsonFile": "target-uf2-968dc26aa6828a0e25ff.json", "name": "uf2", "projectIndex": 0}, {"directoryIndex": 0, "id": "uf2-app::@6890427a1f51a3e7e1df", "jsonFile": "target-uf2-app-8c38747252c10a25ee4a.json", "name": "uf2-app", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build", "source": "/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out"}, "version": {"major": 2, "minor": 4}}