{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/bin/cmake", "cpack": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/bin/cpack", "ctest": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/bin/ctest", "root": "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/share/cmake-3.24"}, "version": {"isDirty": false, "major": 3, "minor": 24, "patch": 0, "string": "3.24.0", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-254e269927eb755473f5.json", "kind": "codemodel", "version": {"major": 2, "minor": 4}}, {"jsonFile": "cache-v2-85d2359a8791c9dd71e7.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "toolchains-v1-947a01e6f15c9474400a.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}], "reply": {"client-cargo": {"cache-v2": {"jsonFile": "cache-v2-85d2359a8791c9dd71e7.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-254e269927eb755473f5.json", "kind": "codemodel", "version": {"major": 2, "minor": 4}}, "toolchains-v1": {"jsonFile": "toolchains-v1-947a01e6f15c9474400a.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}}}}