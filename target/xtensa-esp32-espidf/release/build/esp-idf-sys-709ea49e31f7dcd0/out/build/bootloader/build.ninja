## This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: bootloader
# Configurations: 
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5

# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/

#############################################
# Utility command for menuconfig

build menuconfig: phony CMakeFiles/menuconfig


#############################################
# Utility command for confserver

build confserver: phony CMakeFiles/confserver


#############################################
# Utility command for save-defconfig

build save-defconfig: phony CMakeFiles/save-defconfig


#############################################
# Utility command for gen_project_binary

build gen_project_binary: phony CMakeFiles/gen_project_binary .bin_timestamp bootloader.elf


#############################################
# Utility command for app

build app: phony CMakeFiles/app esp-idf/esptool_py/bootloader_check_size gen_project_binary


#############################################
# Utility command for erase_flash

build erase_flash: phony CMakeFiles/erase_flash


#############################################
# Utility command for monitor

build monitor: phony CMakeFiles/monitor


#############################################
# Utility command for _project_elf_src

build _project_elf_src: phony CMakeFiles/_project_elf_src project_elf_src_esp32.c

# =============================================================================
# Object build statements for EXECUTABLE target bootloader.elf


#############################################
# Order-only phony target for bootloader.elf

build cmake_object_order_depends_target_bootloader.elf: phony || _project_elf_src cmake_object_order_depends_target___idf_main cmake_object_order_depends_target___idf_xtensa project_elf_src_esp32.c

build CMakeFiles/bootloader.elf.dir/project_elf_src_esp32.c.obj: C_COMPILER__bootloader.2eelf_ /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/project_elf_src_esp32.c || cmake_object_order_depends_target_bootloader.elf
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = CMakeFiles/bootloader.elf.dir/project_elf_src_esp32.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject/components/micro-ecc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/platform_port/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/spi_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_app_format/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/esp32/include
  OBJECT_DIR = CMakeFiles/bootloader.elf.dir
  OBJECT_FILE_DIR = CMakeFiles/bootloader.elf.dir


# =============================================================================
# Link build statements for EXECUTABLE target bootloader.elf


#############################################
# Link the executable bootloader.elf

build bootloader.elf: C_EXECUTABLE_LINKER__bootloader.2eelf_ CMakeFiles/bootloader.elf.dir/project_elf_src_esp32.c.obj | esp-idf/xtensa/libxtensa.a esp-idf/soc/libsoc.a esp-idf/micro-ecc/libmicro-ecc.a esp-idf/hal/libhal.a esp-idf/esp_app_format/libesp_app_format.a esp-idf/bootloader_support/libbootloader_support.a esp-idf/efuse/libefuse.a esp-idf/esp_system/libesp_system.a esp-idf/esp_hw_support/libesp_hw_support.a esp-idf/esp_common/libesp_common.a esp-idf/esp_rom/libesp_rom.a esp-idf/log/liblog.a esp-idf/main/libmain.a esp-idf/xtensa/libxtensa.a esp-idf/soc/libsoc.a esp-idf/micro-ecc/libmicro-ecc.a esp-idf/hal/libhal.a esp-idf/esp_app_format/libesp_app_format.a esp-idf/bootloader_support/libbootloader_support.a esp-idf/efuse/libefuse.a esp-idf/esp_system/libesp_system.a esp-idf/esp_hw_support/libesp_hw_support.a esp-idf/esp_common/libesp_common.a esp-idf/esp_rom/libesp_rom.a esp-idf/log/liblog.a esp-idf/xtensa/libxtensa.a esp-idf/soc/libsoc.a esp-idf/micro-ecc/libmicro-ecc.a esp-idf/hal/libhal.a esp-idf/esp_app_format/libesp_app_format.a esp-idf/bootloader_support/libbootloader_support.a esp-idf/efuse/libefuse.a esp-idf/esp_system/libesp_system.a esp-idf/esp_hw_support/libesp_hw_support.a esp-idf/esp_common/libesp_common.a esp-idf/esp_rom/libesp_rom.a esp-idf/log/liblog.a esp-idf/xtensa/libxtensa.a esp-idf/soc/libsoc.a esp-idf/micro-ecc/libmicro-ecc.a esp-idf/hal/libhal.a esp-idf/esp_app_format/libesp_app_format.a esp-idf/bootloader_support/libbootloader_support.a esp-idf/efuse/libefuse.a esp-idf/esp_system/libesp_system.a esp-idf/esp_hw_support/libesp_hw_support.a esp-idf/esp_common/libesp_common.a esp-idf/esp_rom/libesp_rom.a esp-idf/log/liblog.a esp-idf/xtensa/libxtensa.a esp-idf/soc/libsoc.a esp-idf/micro-ecc/libmicro-ecc.a esp-idf/hal/libhal.a esp-idf/esp_app_format/libesp_app_format.a esp-idf/bootloader_support/libbootloader_support.a esp-idf/efuse/libefuse.a esp-idf/esp_system/libesp_system.a esp-idf/esp_hw_support/libesp_hw_support.a esp-idf/esp_common/libesp_common.a esp-idf/esp_rom/libesp_rom.a esp-idf/log/liblog.a /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/libxt_hal.a /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/ld/esp32.peripherals.ld /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32/ld/esp32.rom.ld /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32/ld/esp32.rom.api.ld /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32/ld/esp32.rom.libgcc.ld /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32/ld/esp32.rom.newlib-funcs.ld /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject/main/ld/esp32/bootloader.ld /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject/main/ld/esp32/bootloader.rom.ld || _project_elf_src esp-idf/main/libmain.a esp-idf/xtensa/libxtensa.a
  FLAGS = -mlongcalls -Wno-frame-address
  LINK_LIBRARIES = esp-idf/xtensa/libxtensa.a  esp-idf/soc/libsoc.a  esp-idf/micro-ecc/libmicro-ecc.a  esp-idf/hal/libhal.a  esp-idf/esp_app_format/libesp_app_format.a  esp-idf/bootloader_support/libbootloader_support.a  esp-idf/efuse/libefuse.a  esp-idf/esp_system/libesp_system.a  esp-idf/esp_hw_support/libesp_hw_support.a  esp-idf/esp_common/libesp_common.a  esp-idf/esp_rom/libesp_rom.a  esp-idf/log/liblog.a  esp-idf/main/libmain.a  -Wl,--cref  -Wl,--defsym=IDF_TARGET_ESP32=0  -Wl,--Map="/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/bootloader.map"  -fno-rtti  -fno-lto  -Wl,--gc-sections  -Wl,--warn-common  esp-idf/xtensa/libxtensa.a  esp-idf/soc/libsoc.a  esp-idf/micro-ecc/libmicro-ecc.a  esp-idf/hal/libhal.a  esp-idf/esp_app_format/libesp_app_format.a  esp-idf/bootloader_support/libbootloader_support.a  esp-idf/efuse/libefuse.a  esp-idf/esp_system/libesp_system.a  esp-idf/esp_hw_support/libesp_hw_support.a  esp-idf/esp_common/libesp_common.a  esp-idf/esp_rom/libesp_rom.a  esp-idf/log/liblog.a  esp-idf/xtensa/libxtensa.a  esp-idf/soc/libsoc.a  esp-idf/micro-ecc/libmicro-ecc.a  esp-idf/hal/libhal.a  esp-idf/esp_app_format/libesp_app_format.a  esp-idf/bootloader_support/libbootloader_support.a  esp-idf/efuse/libefuse.a  esp-idf/esp_system/libesp_system.a  esp-idf/esp_hw_support/libesp_hw_support.a  esp-idf/esp_common/libesp_common.a  esp-idf/esp_rom/libesp_rom.a  esp-idf/log/liblog.a  esp-idf/xtensa/libxtensa.a  esp-idf/soc/libsoc.a  esp-idf/micro-ecc/libmicro-ecc.a  esp-idf/hal/libhal.a  esp-idf/esp_app_format/libesp_app_format.a  esp-idf/bootloader_support/libbootloader_support.a  esp-idf/efuse/libefuse.a  esp-idf/esp_system/libesp_system.a  esp-idf/esp_hw_support/libesp_hw_support.a  esp-idf/esp_common/libesp_common.a  esp-idf/esp_rom/libesp_rom.a  esp-idf/log/liblog.a  esp-idf/xtensa/libxtensa.a  esp-idf/soc/libsoc.a  esp-idf/micro-ecc/libmicro-ecc.a  esp-idf/hal/libhal.a  esp-idf/esp_app_format/libesp_app_format.a  esp-idf/bootloader_support/libbootloader_support.a  esp-idf/efuse/libefuse.a  esp-idf/esp_system/libesp_system.a  esp-idf/esp_hw_support/libesp_hw_support.a  esp-idf/esp_common/libesp_common.a  esp-idf/esp_rom/libesp_rom.a  esp-idf/log/liblog.a  /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/libxt_hal.a  -u esp_dport_access_reg_read  -L "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/ld"  -T esp32.peripherals.ld  -u __assert_func  -u abort  -u __ubsan_include  -L "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32/ld"  -T esp32.rom.ld  -T esp32.rom.api.ld  -T esp32.rom.libgcc.ld  -T esp32.rom.newlib-funcs.ld  -L "/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject/main/ld/esp32"  -T bootloader.ld  -T bootloader.rom.ld  -u bootloader_hooks_include
  OBJECT_DIR = CMakeFiles/bootloader.elf.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = bootloader.elf
  TARGET_PDB = bootloader.elf.dbg


#############################################
# Utility command for size

build size: phony CMakeFiles/size


#############################################
# Utility command for size-files

build size-files: phony CMakeFiles/size-files


#############################################
# Utility command for size-components

build size-components: phony CMakeFiles/size-components


#############################################
# Utility command for uf2-app

build uf2-app: phony CMakeFiles/uf2-app gen_project_binary


#############################################
# Utility command for uf2

build uf2: phony CMakeFiles/uf2 gen_project_binary


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/bin/ccmake -S/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject -B/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/bin/cmake --regenerate-during-build -S/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject -B/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util


#############################################
# Custom command for CMakeFiles/menuconfig

build CMakeFiles/menuconfig | ${cmake_ninja_workdir}CMakeFiles/menuconfig: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/python_env/idf5.0_py3.10_env/bin/python /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/tools/kconfig_new/prepare_kconfig_files.py --list-separator=semicolon --env-file /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config.env && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/python_env/idf5.0_py3.10_env/bin/python /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/tools/kconfig_new/confgen.py --list-separator=semicolon --kconfig /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/Kconfig --sdkconfig-rename /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/sdkconfig.rename --config /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/sdkconfig --env-file /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config.env --env IDF_TARGET=esp32 --env IDF_ENV_FPGA= --dont-write-deprecated --output config /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/sdkconfig && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/python_env/idf5.0_py3.10_env/bin/python /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/tools/check_term.py && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/bin/cmake -E env COMPONENT_KCONFIGS_SOURCE_FILE=/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/kconfigs.in COMPONENT_KCONFIGS_PROJBUILD_SOURCE_FILE=/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/kconfigs_projbuild.in KCONFIG_CONFIG=/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/sdkconfig IDF_TARGET=esp32 IDF_ENV_FPGA= /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/python_env/idf5.0_py3.10_env/bin/python -m menuconfig /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/Kconfig && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/python_env/idf5.0_py3.10_env/bin/python /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/tools/kconfig_new/confgen.py --list-separator=semicolon --kconfig /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/Kconfig --sdkconfig-rename /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/sdkconfig.rename --config /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/sdkconfig --env-file /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config.env --env IDF_TARGET=esp32 --env IDF_ENV_FPGA= --output config /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/sdkconfig
  pool = console


#############################################
# Custom command for CMakeFiles/confserver

build CMakeFiles/confserver | ${cmake_ninja_workdir}CMakeFiles/confserver: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/python_env/idf5.0_py3.10_env/bin/python /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/tools/kconfig_new/prepare_kconfig_files.py --list-separator=semicolon --env-file /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config.env && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/python_env/idf5.0_py3.10_env/bin/python /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/tools/kconfig_new/confserver.py --env-file /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config.env --kconfig /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/Kconfig --sdkconfig-rename /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/sdkconfig.rename --config /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/sdkconfig
  pool = console


#############################################
# Custom command for CMakeFiles/save-defconfig

build CMakeFiles/save-defconfig | ${cmake_ninja_workdir}CMakeFiles/save-defconfig: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/python_env/idf5.0_py3.10_env/bin/python /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/tools/kconfig_new/prepare_kconfig_files.py --list-separator=semicolon --env-file /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config.env && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/python_env/idf5.0_py3.10_env/bin/python /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/tools/kconfig_new/confgen.py --list-separator=semicolon --kconfig /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/Kconfig --sdkconfig-rename /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/sdkconfig.rename --config /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/sdkconfig --env-file /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config.env --dont-write-deprecated --output savedefconfig /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject/sdkconfig.defaults
  pool = console


#############################################
# Phony custom command for CMakeFiles/gen_project_binary

build CMakeFiles/gen_project_binary | ${cmake_ninja_workdir}CMakeFiles/gen_project_binary: phony .bin_timestamp || _project_elf_src bootloader.elf esp-idf/bootloader_support/libbootloader_support.a esp-idf/efuse/libefuse.a esp-idf/esp_app_format/libesp_app_format.a esp-idf/esp_common/libesp_common.a esp-idf/esp_hw_support/libesp_hw_support.a esp-idf/esp_rom/libesp_rom.a esp-idf/esp_system/libesp_system.a esp-idf/hal/libhal.a esp-idf/log/liblog.a esp-idf/main/libmain.a esp-idf/micro-ecc/libmicro-ecc.a esp-idf/soc/libsoc.a esp-idf/xtensa/libxtensa.a


#############################################
# Custom command for .bin_timestamp

build .bin_timestamp | ${cmake_ninja_workdir}.bin_timestamp: CUSTOM_COMMAND bootloader.elf || _project_elf_src bootloader.elf esp-idf/bootloader_support/libbootloader_support.a esp-idf/efuse/libefuse.a esp-idf/esp_app_format/libesp_app_format.a esp-idf/esp_common/libesp_common.a esp-idf/esp_hw_support/libesp_hw_support.a esp-idf/esp_rom/libesp_rom.a esp-idf/esp_system/libesp_system.a esp-idf/hal/libhal.a esp-idf/log/liblog.a esp-idf/main/libmain.a esp-idf/micro-ecc/libmicro-ecc.a esp-idf/soc/libsoc.a esp-idf/xtensa/libxtensa.a
  COMMAND = cd /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/python_env/idf5.0_py3.10_env/bin/python /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esptool_py/esptool/esptool.py --chip esp32 elf2image --flash_mode dio --flash_freq 40m --flash_size 2MB --min-rev-full 0 --max-rev-full 399 -o /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/bootloader.bin /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/bootloader.elf && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/bin/cmake -E echo "Generated /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/bootloader.bin" && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/bin/cmake -E md5sum /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/bootloader.bin > /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/.bin_timestamp
  DESC = Generating binary image from built executable
  restat = 1


#############################################
# Phony custom command for CMakeFiles/app

build CMakeFiles/app | ${cmake_ninja_workdir}CMakeFiles/app: phony || _project_elf_src bootloader.elf esp-idf/bootloader_support/libbootloader_support.a esp-idf/efuse/libefuse.a esp-idf/esp_app_format/libesp_app_format.a esp-idf/esp_common/libesp_common.a esp-idf/esp_hw_support/libesp_hw_support.a esp-idf/esp_rom/libesp_rom.a esp-idf/esp_system/libesp_system.a esp-idf/esptool_py/bootloader_check_size esp-idf/hal/libhal.a esp-idf/log/liblog.a esp-idf/main/libmain.a esp-idf/micro-ecc/libmicro-ecc.a esp-idf/soc/libsoc.a esp-idf/xtensa/libxtensa.a gen_project_binary


#############################################
# Custom command for CMakeFiles/erase_flash

build CMakeFiles/erase_flash | ${cmake_ninja_workdir}CMakeFiles/erase_flash: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esptool_py && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/bin/cmake -D IDF_PATH=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4 -D "SERIAL_TOOL=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/python_env/idf5.0_py3.10_env/bin/python;;/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esptool_py/esptool/esptool.py;--chip;esp32" -D SERIAL_TOOL_ARGS=erase_flash -P run_serial_tool.cmake
  pool = console


#############################################
# Custom command for CMakeFiles/monitor

build CMakeFiles/monitor | ${cmake_ninja_workdir}CMakeFiles/monitor: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esptool_py && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/bin/cmake -D IDF_PATH=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4 -D "SERIAL_TOOL=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/python_env/idf5.0_py3.10_env/bin/python;/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/tools/idf_monitor.py" -D "SERIAL_TOOL_ARGS=--target;esp32;--revision;0;/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/bootloader.elf" -D WORKING_DIRECTORY=/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader -P run_serial_tool.cmake
  pool = console


#############################################
# Phony custom command for CMakeFiles/_project_elf_src

build CMakeFiles/_project_elf_src | ${cmake_ninja_workdir}CMakeFiles/_project_elf_src: phony project_elf_src_esp32.c


#############################################
# Custom command for project_elf_src_esp32.c

build project_elf_src_esp32.c | ${cmake_ninja_workdir}project_elf_src_esp32.c: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/bin/cmake -E touch /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/project_elf_src_esp32.c
  DESC = Generating project_elf_src_esp32.c
  restat = 1


#############################################
# Custom command for CMakeFiles/size

build CMakeFiles/size | ${cmake_ninja_workdir}CMakeFiles/size: CUSTOM_COMMAND bootloader.map
  COMMAND = cd /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/python_env/idf5.0_py3.10_env/bin/python /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/tools/idf_size.py /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/bootloader.map


#############################################
# Custom command for CMakeFiles/size-files

build CMakeFiles/size-files | ${cmake_ninja_workdir}CMakeFiles/size-files: CUSTOM_COMMAND bootloader.map
  COMMAND = cd /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/python_env/idf5.0_py3.10_env/bin/python /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/tools/idf_size.py --files /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/bootloader.map


#############################################
# Custom command for CMakeFiles/size-components

build CMakeFiles/size-components | ${cmake_ninja_workdir}CMakeFiles/size-components: CUSTOM_COMMAND bootloader.map
  COMMAND = cd /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/python_env/idf5.0_py3.10_env/bin/python /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/tools/idf_size.py --archives /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/bootloader.map


#############################################
# Custom command for CMakeFiles/uf2-app

build CMakeFiles/uf2-app | ${cmake_ninja_workdir}CMakeFiles/uf2-app: CUSTOM_COMMAND || _project_elf_src bootloader.elf esp-idf/bootloader_support/libbootloader_support.a esp-idf/efuse/libefuse.a esp-idf/esp_app_format/libesp_app_format.a esp-idf/esp_common/libesp_common.a esp-idf/esp_hw_support/libesp_hw_support.a esp-idf/esp_rom/libesp_rom.a esp-idf/esp_system/libesp_system.a esp-idf/hal/libhal.a esp-idf/log/liblog.a esp-idf/main/libmain.a esp-idf/micro-ecc/libmicro-ecc.a esp-idf/soc/libsoc.a esp-idf/xtensa/libxtensa.a gen_project_binary
  COMMAND = cd /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/python_env/idf5.0_py3.10_env/bin/python /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/tools/mkuf2.py write -o /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/uf2-app.bin --json /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/flasher_args.json --chip-id 0x1c5f21b0 --bin app
  pool = console


#############################################
# Custom command for CMakeFiles/uf2

build CMakeFiles/uf2 | ${cmake_ninja_workdir}CMakeFiles/uf2: CUSTOM_COMMAND bootloader || _project_elf_src bootloader.elf esp-idf/bootloader_support/libbootloader_support.a esp-idf/efuse/libefuse.a esp-idf/esp_app_format/libesp_app_format.a esp-idf/esp_common/libesp_common.a esp-idf/esp_hw_support/libesp_hw_support.a esp-idf/esp_rom/libesp_rom.a esp-idf/esp_system/libesp_system.a esp-idf/hal/libhal.a esp-idf/log/liblog.a esp-idf/main/libmain.a esp-idf/micro-ecc/libmicro-ecc.a esp-idf/soc/libsoc.a esp-idf/xtensa/libxtensa.a gen_project_binary
  COMMAND = cd /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/python_env/idf5.0_py3.10_env/bin/python /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/tools/mkuf2.py write -o /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/uf2.bin --json /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/flasher_args.json --chip-id 0x1c5f21b0
  pool = console

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for edit_cache

build esp-idf/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/bin/ccmake -S/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject -B/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build esp-idf/edit_cache: phony esp-idf/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build esp-idf/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/bin/cmake --regenerate-during-build -S/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject -B/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build esp-idf/rebuild_cache: phony esp-idf/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target __idf_xtensa


#############################################
# Order-only phony target for __idf_xtensa

build cmake_object_order_depends_target___idf_xtensa: phony || cmake_object_order_depends_target___idf_soc

build esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/eri.c.obj: C_COMPILER____idf_xtensa_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/eri.c || cmake_object_order_depends_target___idf_xtensa
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/eri.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include
  OBJECT_DIR = esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir
  OBJECT_FILE_DIR = esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir

build esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/xt_trax.c.obj: C_COMPILER____idf_xtensa_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/xt_trax.c || cmake_object_order_depends_target___idf_xtensa
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/xt_trax.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include
  OBJECT_DIR = esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir
  OBJECT_FILE_DIR = esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir


# =============================================================================
# Link build statements for STATIC_LIBRARY target __idf_xtensa


#############################################
# Link the static library esp-idf/xtensa/libxtensa.a

build esp-idf/xtensa/libxtensa.a: C_STATIC_LIBRARY_LINKER____idf_xtensa_ esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/eri.c.obj esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/xt_trax.c.obj || esp-idf/soc/libsoc.a
  LANGUAGE_COMPILE_FLAGS = -mlongcalls -Wno-frame-address
  OBJECT_DIR = esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = esp-idf/xtensa/libxtensa.a
  TARGET_PDB = xtensa.a.dbg


#############################################
# Utility command for edit_cache

build esp-idf/xtensa/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/xtensa && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/bin/ccmake -S/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject -B/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build esp-idf/xtensa/edit_cache: phony esp-idf/xtensa/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build esp-idf/xtensa/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/xtensa && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/bin/cmake --regenerate-during-build -S/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject -B/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build esp-idf/xtensa/rebuild_cache: phony esp-idf/xtensa/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for edit_cache

build esp-idf/newlib/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/newlib && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/bin/ccmake -S/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject -B/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build esp-idf/newlib/edit_cache: phony esp-idf/newlib/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build esp-idf/newlib/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/newlib && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/bin/cmake --regenerate-during-build -S/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject -B/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build esp-idf/newlib/rebuild_cache: phony esp-idf/newlib/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target __idf_soc


#############################################
# Order-only phony target for __idf_soc

build cmake_object_order_depends_target___idf_soc: phony || cmake_object_order_depends_target___idf_micro-ecc

build esp-idf/soc/CMakeFiles/__idf_soc.dir/lldesc.c.obj: C_COMPILER____idf_soc_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/lldesc.c || cmake_object_order_depends_target___idf_soc
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/soc/CMakeFiles/__idf_soc.dir/lldesc.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include
  OBJECT_DIR = esp-idf/soc/CMakeFiles/__idf_soc.dir
  OBJECT_FILE_DIR = esp-idf/soc/CMakeFiles/__idf_soc.dir

build esp-idf/soc/CMakeFiles/__idf_soc.dir/dport_access_common.c.obj: C_COMPILER____idf_soc_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/dport_access_common.c || cmake_object_order_depends_target___idf_soc
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/soc/CMakeFiles/__idf_soc.dir/dport_access_common.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include
  OBJECT_DIR = esp-idf/soc/CMakeFiles/__idf_soc.dir
  OBJECT_FILE_DIR = esp-idf/soc/CMakeFiles/__idf_soc.dir

build esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/adc_periph.c.obj: C_COMPILER____idf_soc_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/adc_periph.c || cmake_object_order_depends_target___idf_soc
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/adc_periph.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include
  OBJECT_DIR = esp-idf/soc/CMakeFiles/__idf_soc.dir
  OBJECT_FILE_DIR = esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32

build esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/dac_periph.c.obj: C_COMPILER____idf_soc_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/dac_periph.c || cmake_object_order_depends_target___idf_soc
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/dac_periph.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include
  OBJECT_DIR = esp-idf/soc/CMakeFiles/__idf_soc.dir
  OBJECT_FILE_DIR = esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32

build esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/dport_access.c.obj: C_COMPILER____idf_soc_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/dport_access.c || cmake_object_order_depends_target___idf_soc
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/dport_access.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include
  OBJECT_DIR = esp-idf/soc/CMakeFiles/__idf_soc.dir
  OBJECT_FILE_DIR = esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32

build esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/gpio_periph.c.obj: C_COMPILER____idf_soc_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/gpio_periph.c || cmake_object_order_depends_target___idf_soc
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/gpio_periph.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include
  OBJECT_DIR = esp-idf/soc/CMakeFiles/__idf_soc.dir
  OBJECT_FILE_DIR = esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32

build esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/sdm_periph.c.obj: C_COMPILER____idf_soc_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/sdm_periph.c || cmake_object_order_depends_target___idf_soc
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/sdm_periph.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include
  OBJECT_DIR = esp-idf/soc/CMakeFiles/__idf_soc.dir
  OBJECT_FILE_DIR = esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32

build esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/i2c_periph.c.obj: C_COMPILER____idf_soc_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/i2c_periph.c || cmake_object_order_depends_target___idf_soc
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/i2c_periph.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include
  OBJECT_DIR = esp-idf/soc/CMakeFiles/__idf_soc.dir
  OBJECT_FILE_DIR = esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32

build esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/i2s_periph.c.obj: C_COMPILER____idf_soc_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/i2s_periph.c || cmake_object_order_depends_target___idf_soc
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/i2s_periph.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include
  OBJECT_DIR = esp-idf/soc/CMakeFiles/__idf_soc.dir
  OBJECT_FILE_DIR = esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32

build esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/interrupts.c.obj: C_COMPILER____idf_soc_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/interrupts.c || cmake_object_order_depends_target___idf_soc
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/interrupts.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include
  OBJECT_DIR = esp-idf/soc/CMakeFiles/__idf_soc.dir
  OBJECT_FILE_DIR = esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32

build esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/lcd_periph.c.obj: C_COMPILER____idf_soc_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/lcd_periph.c || cmake_object_order_depends_target___idf_soc
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/lcd_periph.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include
  OBJECT_DIR = esp-idf/soc/CMakeFiles/__idf_soc.dir
  OBJECT_FILE_DIR = esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32

build esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/ledc_periph.c.obj: C_COMPILER____idf_soc_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/ledc_periph.c || cmake_object_order_depends_target___idf_soc
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/ledc_periph.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include
  OBJECT_DIR = esp-idf/soc/CMakeFiles/__idf_soc.dir
  OBJECT_FILE_DIR = esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32

build esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/mcpwm_periph.c.obj: C_COMPILER____idf_soc_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/mcpwm_periph.c || cmake_object_order_depends_target___idf_soc
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/mcpwm_periph.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include
  OBJECT_DIR = esp-idf/soc/CMakeFiles/__idf_soc.dir
  OBJECT_FILE_DIR = esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32

build esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/pcnt_periph.c.obj: C_COMPILER____idf_soc_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/pcnt_periph.c || cmake_object_order_depends_target___idf_soc
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/pcnt_periph.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include
  OBJECT_DIR = esp-idf/soc/CMakeFiles/__idf_soc.dir
  OBJECT_FILE_DIR = esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32

build esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/rmt_periph.c.obj: C_COMPILER____idf_soc_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/rmt_periph.c || cmake_object_order_depends_target___idf_soc
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/rmt_periph.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include
  OBJECT_DIR = esp-idf/soc/CMakeFiles/__idf_soc.dir
  OBJECT_FILE_DIR = esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32

build esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/rtc_io_periph.c.obj: C_COMPILER____idf_soc_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/rtc_io_periph.c || cmake_object_order_depends_target___idf_soc
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/rtc_io_periph.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include
  OBJECT_DIR = esp-idf/soc/CMakeFiles/__idf_soc.dir
  OBJECT_FILE_DIR = esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32

build esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/sdio_slave_periph.c.obj: C_COMPILER____idf_soc_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/sdio_slave_periph.c || cmake_object_order_depends_target___idf_soc
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/sdio_slave_periph.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include
  OBJECT_DIR = esp-idf/soc/CMakeFiles/__idf_soc.dir
  OBJECT_FILE_DIR = esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32

build esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/sdmmc_periph.c.obj: C_COMPILER____idf_soc_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/sdmmc_periph.c || cmake_object_order_depends_target___idf_soc
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/sdmmc_periph.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include
  OBJECT_DIR = esp-idf/soc/CMakeFiles/__idf_soc.dir
  OBJECT_FILE_DIR = esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32

build esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/spi_periph.c.obj: C_COMPILER____idf_soc_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/spi_periph.c || cmake_object_order_depends_target___idf_soc
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/spi_periph.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include
  OBJECT_DIR = esp-idf/soc/CMakeFiles/__idf_soc.dir
  OBJECT_FILE_DIR = esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32

build esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/timer_periph.c.obj: C_COMPILER____idf_soc_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/timer_periph.c || cmake_object_order_depends_target___idf_soc
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/timer_periph.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include
  OBJECT_DIR = esp-idf/soc/CMakeFiles/__idf_soc.dir
  OBJECT_FILE_DIR = esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32

build esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/touch_sensor_periph.c.obj: C_COMPILER____idf_soc_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/touch_sensor_periph.c || cmake_object_order_depends_target___idf_soc
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/touch_sensor_periph.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include
  OBJECT_DIR = esp-idf/soc/CMakeFiles/__idf_soc.dir
  OBJECT_FILE_DIR = esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32

build esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/uart_periph.c.obj: C_COMPILER____idf_soc_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/uart_periph.c || cmake_object_order_depends_target___idf_soc
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/uart_periph.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include
  OBJECT_DIR = esp-idf/soc/CMakeFiles/__idf_soc.dir
  OBJECT_FILE_DIR = esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32


# =============================================================================
# Link build statements for STATIC_LIBRARY target __idf_soc


#############################################
# Link the static library esp-idf/soc/libsoc.a

build esp-idf/soc/libsoc.a: C_STATIC_LIBRARY_LINKER____idf_soc_ esp-idf/soc/CMakeFiles/__idf_soc.dir/lldesc.c.obj esp-idf/soc/CMakeFiles/__idf_soc.dir/dport_access_common.c.obj esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/adc_periph.c.obj esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/dac_periph.c.obj esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/dport_access.c.obj esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/gpio_periph.c.obj esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/sdm_periph.c.obj esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/i2c_periph.c.obj esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/i2s_periph.c.obj esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/interrupts.c.obj esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/lcd_periph.c.obj esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/ledc_periph.c.obj esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/mcpwm_periph.c.obj esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/pcnt_periph.c.obj esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/rmt_periph.c.obj esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/rtc_io_periph.c.obj esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/sdio_slave_periph.c.obj esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/sdmmc_periph.c.obj esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/spi_periph.c.obj esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/timer_periph.c.obj esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/touch_sensor_periph.c.obj esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/uart_periph.c.obj || esp-idf/micro-ecc/libmicro-ecc.a
  LANGUAGE_COMPILE_FLAGS = -mlongcalls -Wno-frame-address
  OBJECT_DIR = esp-idf/soc/CMakeFiles/__idf_soc.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = esp-idf/soc/libsoc.a
  TARGET_PDB = soc.a.dbg


#############################################
# Utility command for edit_cache

build esp-idf/soc/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/soc && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/bin/ccmake -S/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject -B/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build esp-idf/soc/edit_cache: phony esp-idf/soc/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build esp-idf/soc/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/soc && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/bin/cmake --regenerate-during-build -S/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject -B/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build esp-idf/soc/rebuild_cache: phony esp-idf/soc/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for edit_cache

build esp-idf/soc/esp32/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/soc/esp32 && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/bin/ccmake -S/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject -B/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build esp-idf/soc/esp32/edit_cache: phony esp-idf/soc/esp32/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build esp-idf/soc/esp32/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/soc/esp32 && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/bin/cmake --regenerate-during-build -S/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject -B/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build esp-idf/soc/esp32/rebuild_cache: phony esp-idf/soc/esp32/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target __idf_micro-ecc


#############################################
# Order-only phony target for __idf_micro-ecc

build cmake_object_order_depends_target___idf_micro-ecc: phony || cmake_object_order_depends_target___idf_hal

build esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir/uECC_verify_antifault.c.obj: C_COMPILER____idf_micro-ecc_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject/components/micro-ecc/uECC_verify_antifault.c || cmake_object_order_depends_target___idf_micro-ecc
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir/uECC_verify_antifault.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject/components/micro-ecc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include
  OBJECT_DIR = esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir
  OBJECT_FILE_DIR = esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir


# =============================================================================
# Link build statements for STATIC_LIBRARY target __idf_micro-ecc


#############################################
# Link the static library esp-idf/micro-ecc/libmicro-ecc.a

build esp-idf/micro-ecc/libmicro-ecc.a: C_STATIC_LIBRARY_LINKER____idf_micro-ecc_ esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir/uECC_verify_antifault.c.obj || esp-idf/hal/libhal.a
  LANGUAGE_COMPILE_FLAGS = -mlongcalls -Wno-frame-address
  OBJECT_DIR = esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = esp-idf/micro-ecc/libmicro-ecc.a
  TARGET_PDB = micro-ecc.a.dbg


#############################################
# Utility command for edit_cache

build esp-idf/micro-ecc/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/micro-ecc && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/bin/ccmake -S/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject -B/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build esp-idf/micro-ecc/edit_cache: phony esp-idf/micro-ecc/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build esp-idf/micro-ecc/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/micro-ecc && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/bin/cmake --regenerate-during-build -S/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject -B/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build esp-idf/micro-ecc/rebuild_cache: phony esp-idf/micro-ecc/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target __idf_hal


#############################################
# Order-only phony target for __idf_hal

build cmake_object_order_depends_target___idf_hal: phony || cmake_object_order_depends_target___idf_esp_app_format

build esp-idf/hal/CMakeFiles/__idf_hal.dir/mpu_hal.c.obj: C_COMPILER____idf_hal_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/mpu_hal.c || cmake_object_order_depends_target___idf_hal
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/hal/CMakeFiles/__idf_hal.dir/mpu_hal.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -Wno-format
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/platform_port/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include
  OBJECT_DIR = esp-idf/hal/CMakeFiles/__idf_hal.dir
  OBJECT_FILE_DIR = esp-idf/hal/CMakeFiles/__idf_hal.dir

build esp-idf/hal/CMakeFiles/__idf_hal.dir/efuse_hal.c.obj: C_COMPILER____idf_hal_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/efuse_hal.c || cmake_object_order_depends_target___idf_hal
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/hal/CMakeFiles/__idf_hal.dir/efuse_hal.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -Wno-format
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/platform_port/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include
  OBJECT_DIR = esp-idf/hal/CMakeFiles/__idf_hal.dir
  OBJECT_FILE_DIR = esp-idf/hal/CMakeFiles/__idf_hal.dir

build esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32/efuse_hal.c.obj: C_COMPILER____idf_hal_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/esp32/efuse_hal.c || cmake_object_order_depends_target___idf_hal
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32/efuse_hal.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -Wno-format
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/platform_port/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include
  OBJECT_DIR = esp-idf/hal/CMakeFiles/__idf_hal.dir
  OBJECT_FILE_DIR = esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32

build esp-idf/hal/CMakeFiles/__idf_hal.dir/mmu_hal.c.obj: C_COMPILER____idf_hal_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/mmu_hal.c || cmake_object_order_depends_target___idf_hal
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/hal/CMakeFiles/__idf_hal.dir/mmu_hal.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -Wno-format
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/platform_port/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include
  OBJECT_DIR = esp-idf/hal/CMakeFiles/__idf_hal.dir
  OBJECT_FILE_DIR = esp-idf/hal/CMakeFiles/__idf_hal.dir

build esp-idf/hal/CMakeFiles/__idf_hal.dir/wdt_hal_iram.c.obj: C_COMPILER____idf_hal_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/wdt_hal_iram.c || cmake_object_order_depends_target___idf_hal
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/hal/CMakeFiles/__idf_hal.dir/wdt_hal_iram.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -Wno-format
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/platform_port/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include
  OBJECT_DIR = esp-idf/hal/CMakeFiles/__idf_hal.dir
  OBJECT_FILE_DIR = esp-idf/hal/CMakeFiles/__idf_hal.dir

build esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32/cache_hal_esp32.c.obj: C_COMPILER____idf_hal_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/esp32/cache_hal_esp32.c || cmake_object_order_depends_target___idf_hal
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32/cache_hal_esp32.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -Wno-format
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/platform_port/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include
  OBJECT_DIR = esp-idf/hal/CMakeFiles/__idf_hal.dir
  OBJECT_FILE_DIR = esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32


# =============================================================================
# Link build statements for STATIC_LIBRARY target __idf_hal


#############################################
# Link the static library esp-idf/hal/libhal.a

build esp-idf/hal/libhal.a: C_STATIC_LIBRARY_LINKER____idf_hal_ esp-idf/hal/CMakeFiles/__idf_hal.dir/mpu_hal.c.obj esp-idf/hal/CMakeFiles/__idf_hal.dir/efuse_hal.c.obj esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32/efuse_hal.c.obj esp-idf/hal/CMakeFiles/__idf_hal.dir/mmu_hal.c.obj esp-idf/hal/CMakeFiles/__idf_hal.dir/wdt_hal_iram.c.obj esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32/cache_hal_esp32.c.obj || esp-idf/esp_app_format/libesp_app_format.a
  LANGUAGE_COMPILE_FLAGS = -mlongcalls -Wno-frame-address
  OBJECT_DIR = esp-idf/hal/CMakeFiles/__idf_hal.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = esp-idf/hal/libhal.a
  TARGET_PDB = hal.a.dbg


#############################################
# Utility command for edit_cache

build esp-idf/hal/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/hal && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/bin/ccmake -S/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject -B/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build esp-idf/hal/edit_cache: phony esp-idf/hal/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build esp-idf/hal/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/hal && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/bin/cmake --regenerate-during-build -S/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject -B/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build esp-idf/hal/rebuild_cache: phony esp-idf/hal/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for edit_cache

build esp-idf/spi_flash/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/spi_flash && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/bin/ccmake -S/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject -B/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build esp-idf/spi_flash/edit_cache: phony esp-idf/spi_flash/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build esp-idf/spi_flash/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/spi_flash && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/bin/cmake --regenerate-during-build -S/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject -B/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build esp-idf/spi_flash/rebuild_cache: phony esp-idf/spi_flash/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target __idf_esp_app_format


#############################################
# Order-only phony target for __idf_esp_app_format

build cmake_object_order_depends_target___idf_esp_app_format: phony || cmake_object_order_depends_target___idf_bootloader_support

build esp-idf/esp_app_format/CMakeFiles/__idf_esp_app_format.dir/esp_app_desc.c.obj: C_COMPILER____idf_esp_app_format_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_app_format/esp_app_desc.c || cmake_object_order_depends_target___idf_esp_app_format
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D PROJECT_NAME=\"bootloader\" -DPROJECT_VER=\"v5.0.4-dirty\"
  DEP_FILE = esp-idf/esp_app_format/CMakeFiles/__idf_esp_app_format.dir/esp_app_desc.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_app_format/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include
  OBJECT_DIR = esp-idf/esp_app_format/CMakeFiles/__idf_esp_app_format.dir
  OBJECT_FILE_DIR = esp-idf/esp_app_format/CMakeFiles/__idf_esp_app_format.dir


# =============================================================================
# Link build statements for STATIC_LIBRARY target __idf_esp_app_format


#############################################
# Link the static library esp-idf/esp_app_format/libesp_app_format.a

build esp-idf/esp_app_format/libesp_app_format.a: C_STATIC_LIBRARY_LINKER____idf_esp_app_format_ esp-idf/esp_app_format/CMakeFiles/__idf_esp_app_format.dir/esp_app_desc.c.obj || esp-idf/bootloader_support/libbootloader_support.a
  LANGUAGE_COMPILE_FLAGS = -mlongcalls -Wno-frame-address
  OBJECT_DIR = esp-idf/esp_app_format/CMakeFiles/__idf_esp_app_format.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = esp-idf/esp_app_format/libesp_app_format.a
  TARGET_PDB = esp_app_format.a.dbg


#############################################
# Utility command for edit_cache

build esp-idf/esp_app_format/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/esp_app_format && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/bin/ccmake -S/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject -B/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build esp-idf/esp_app_format/edit_cache: phony esp-idf/esp_app_format/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build esp-idf/esp_app_format/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/esp_app_format && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/bin/cmake --regenerate-during-build -S/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject -B/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build esp-idf/esp_app_format/rebuild_cache: phony esp-idf/esp_app_format/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target __idf_bootloader_support


#############################################
# Order-only phony target for __idf_bootloader_support

build cmake_object_order_depends_target___idf_bootloader_support: phony || cmake_object_order_depends_target___idf_efuse

build esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common.c.obj: C_COMPILER____idf_bootloader_support_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/src/bootloader_common.c || cmake_object_order_depends_target___idf_bootloader_support
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -Wno-format
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject/components/micro-ecc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/spi_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/platform_port/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_app_format/include
  OBJECT_DIR = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir
  OBJECT_FILE_DIR = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src

build esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common_loader.c.obj: C_COMPILER____idf_bootloader_support_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/src/bootloader_common_loader.c || cmake_object_order_depends_target___idf_bootloader_support
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common_loader.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -Wno-format
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject/components/micro-ecc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/spi_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/platform_port/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_app_format/include
  OBJECT_DIR = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir
  OBJECT_FILE_DIR = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src

build esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_init.c.obj: C_COMPILER____idf_bootloader_support_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/src/bootloader_clock_init.c || cmake_object_order_depends_target___idf_bootloader_support
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_init.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -Wno-format
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject/components/micro-ecc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/spi_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/platform_port/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_app_format/include
  OBJECT_DIR = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir
  OBJECT_FILE_DIR = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src

build esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash.c.obj: C_COMPILER____idf_bootloader_support_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/bootloader_flash/src/bootloader_flash.c || cmake_object_order_depends_target___idf_bootloader_support
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -Wno-format
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject/components/micro-ecc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/spi_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/platform_port/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_app_format/include
  OBJECT_DIR = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir
  OBJECT_FILE_DIR = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src

build esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_mem.c.obj: C_COMPILER____idf_bootloader_support_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/src/bootloader_mem.c || cmake_object_order_depends_target___idf_bootloader_support
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_mem.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -Wno-format
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject/components/micro-ecc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/spi_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/platform_port/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_app_format/include
  OBJECT_DIR = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir
  OBJECT_FILE_DIR = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src

build esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random.c.obj: C_COMPILER____idf_bootloader_support_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/src/bootloader_random.c || cmake_object_order_depends_target___idf_bootloader_support
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -Wno-format
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject/components/micro-ecc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/spi_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/platform_port/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_app_format/include
  OBJECT_DIR = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir
  OBJECT_FILE_DIR = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src

build esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random_esp32.c.obj: C_COMPILER____idf_bootloader_support_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/src/bootloader_random_esp32.c || cmake_object_order_depends_target___idf_bootloader_support
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random_esp32.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -Wno-format
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject/components/micro-ecc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/spi_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/platform_port/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_app_format/include
  OBJECT_DIR = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir
  OBJECT_FILE_DIR = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src

build esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_utility.c.obj: C_COMPILER____idf_bootloader_support_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/src/bootloader_utility.c || cmake_object_order_depends_target___idf_bootloader_support
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_utility.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -Wno-format
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject/components/micro-ecc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/spi_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/platform_port/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_app_format/include
  OBJECT_DIR = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir
  OBJECT_FILE_DIR = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src

build esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp_image_format.c.obj: C_COMPILER____idf_bootloader_support_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/src/esp_image_format.c || cmake_object_order_depends_target___idf_bootloader_support
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp_image_format.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -Wno-format
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject/components/micro-ecc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/spi_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/platform_port/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_app_format/include
  OBJECT_DIR = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir
  OBJECT_FILE_DIR = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src

build esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_encrypt.c.obj: C_COMPILER____idf_bootloader_support_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/src/flash_encrypt.c || cmake_object_order_depends_target___idf_bootloader_support
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_encrypt.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -Wno-format
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject/components/micro-ecc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/spi_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/platform_port/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_app_format/include
  OBJECT_DIR = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir
  OBJECT_FILE_DIR = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src

build esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/secure_boot.c.obj: C_COMPILER____idf_bootloader_support_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/src/secure_boot.c || cmake_object_order_depends_target___idf_bootloader_support
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/secure_boot.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -Wno-format
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject/components/micro-ecc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/spi_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/platform_port/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_app_format/include
  OBJECT_DIR = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir
  OBJECT_FILE_DIR = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src

build esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_partitions.c.obj: C_COMPILER____idf_bootloader_support_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/src/flash_partitions.c || cmake_object_order_depends_target___idf_bootloader_support
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_partitions.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -Wno-format
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject/components/micro-ecc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/spi_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/platform_port/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_app_format/include
  OBJECT_DIR = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir
  OBJECT_FILE_DIR = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src

build esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/flash_qio_mode.c.obj: C_COMPILER____idf_bootloader_support_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/bootloader_flash/src/flash_qio_mode.c || cmake_object_order_depends_target___idf_bootloader_support
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/flash_qio_mode.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -Wno-format
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject/components/micro-ecc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/spi_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/platform_port/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_app_format/include
  OBJECT_DIR = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir
  OBJECT_FILE_DIR = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src

build esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash_config_esp32.c.obj: C_COMPILER____idf_bootloader_support_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/bootloader_flash/src/bootloader_flash_config_esp32.c || cmake_object_order_depends_target___idf_bootloader_support
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash_config_esp32.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -Wno-format
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject/components/micro-ecc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/spi_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/platform_port/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_app_format/include
  OBJECT_DIR = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir
  OBJECT_FILE_DIR = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src

build esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_efuse.c.obj: C_COMPILER____idf_bootloader_support_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/src/bootloader_efuse.c || cmake_object_order_depends_target___idf_bootloader_support
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_efuse.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -Wno-format
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject/components/micro-ecc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/spi_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/platform_port/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_app_format/include
  OBJECT_DIR = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir
  OBJECT_FILE_DIR = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src

build esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_init.c.obj: C_COMPILER____idf_bootloader_support_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/src/bootloader_init.c || cmake_object_order_depends_target___idf_bootloader_support
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_init.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -Wno-format
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject/components/micro-ecc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/spi_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/platform_port/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_app_format/include
  OBJECT_DIR = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir
  OBJECT_FILE_DIR = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src

build esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_loader.c.obj: C_COMPILER____idf_bootloader_support_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/src/bootloader_clock_loader.c || cmake_object_order_depends_target___idf_bootloader_support
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_loader.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -Wno-format
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject/components/micro-ecc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/spi_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/platform_port/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_app_format/include
  OBJECT_DIR = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir
  OBJECT_FILE_DIR = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src

build esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console.c.obj: C_COMPILER____idf_bootloader_support_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/src/bootloader_console.c || cmake_object_order_depends_target___idf_bootloader_support
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -Wno-format
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject/components/micro-ecc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/spi_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/platform_port/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_app_format/include
  OBJECT_DIR = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir
  OBJECT_FILE_DIR = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src

build esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console_loader.c.obj: C_COMPILER____idf_bootloader_support_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/src/bootloader_console_loader.c || cmake_object_order_depends_target___idf_bootloader_support
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console_loader.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -Wno-format
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject/components/micro-ecc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/spi_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/platform_port/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_app_format/include
  OBJECT_DIR = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir
  OBJECT_FILE_DIR = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src

build esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_panic.c.obj: C_COMPILER____idf_bootloader_support_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/src/bootloader_panic.c || cmake_object_order_depends_target___idf_bootloader_support
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_panic.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -Wno-format
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject/components/micro-ecc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/spi_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/platform_port/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_app_format/include
  OBJECT_DIR = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir
  OBJECT_FILE_DIR = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src

build esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32/bootloader_sha.c.obj: C_COMPILER____idf_bootloader_support_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/src/esp32/bootloader_sha.c || cmake_object_order_depends_target___idf_bootloader_support
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32/bootloader_sha.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -Wno-format
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject/components/micro-ecc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/spi_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/platform_port/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_app_format/include
  OBJECT_DIR = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir
  OBJECT_FILE_DIR = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32

build esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32/bootloader_soc.c.obj: C_COMPILER____idf_bootloader_support_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/src/esp32/bootloader_soc.c || cmake_object_order_depends_target___idf_bootloader_support
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32/bootloader_soc.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -Wno-format
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject/components/micro-ecc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/spi_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/platform_port/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_app_format/include
  OBJECT_DIR = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir
  OBJECT_FILE_DIR = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32

build esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32/bootloader_esp32.c.obj: C_COMPILER____idf_bootloader_support_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/src/esp32/bootloader_esp32.c || cmake_object_order_depends_target___idf_bootloader_support
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32/bootloader_esp32.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -Wno-format
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject/components/micro-ecc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject/components/micro-ecc/micro-ecc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/spi_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/platform_port/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_app_format/include
  OBJECT_DIR = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir
  OBJECT_FILE_DIR = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32


# =============================================================================
# Link build statements for STATIC_LIBRARY target __idf_bootloader_support


#############################################
# Link the static library esp-idf/bootloader_support/libbootloader_support.a

build esp-idf/bootloader_support/libbootloader_support.a: C_STATIC_LIBRARY_LINKER____idf_bootloader_support_ esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common.c.obj esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common_loader.c.obj esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_init.c.obj esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash.c.obj esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_mem.c.obj esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random.c.obj esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random_esp32.c.obj esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_utility.c.obj esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp_image_format.c.obj esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_encrypt.c.obj esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/secure_boot.c.obj esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_partitions.c.obj esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/flash_qio_mode.c.obj esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash_config_esp32.c.obj esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_efuse.c.obj esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_init.c.obj esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_loader.c.obj esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console.c.obj esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console_loader.c.obj esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_panic.c.obj esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32/bootloader_sha.c.obj esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32/bootloader_soc.c.obj esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32/bootloader_esp32.c.obj || esp-idf/efuse/libefuse.a
  LANGUAGE_COMPILE_FLAGS = -mlongcalls -Wno-frame-address
  OBJECT_DIR = esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = esp-idf/bootloader_support/libbootloader_support.a
  TARGET_PDB = bootloader_support.a.dbg


#############################################
# Utility command for edit_cache

build esp-idf/bootloader_support/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/bootloader_support && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/bin/ccmake -S/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject -B/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build esp-idf/bootloader_support/edit_cache: phony esp-idf/bootloader_support/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build esp-idf/bootloader_support/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/bootloader_support && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/bin/cmake --regenerate-during-build -S/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject -B/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build esp-idf/bootloader_support/rebuild_cache: phony esp-idf/bootloader_support/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target __idf_efuse


#############################################
# Order-only phony target for __idf_efuse

build cmake_object_order_depends_target___idf_efuse: phony || cmake_object_order_depends_target___idf_esp_system

build esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32/esp_efuse_table.c.obj: C_COMPILER____idf_efuse_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/esp32/esp_efuse_table.c || cmake_object_order_depends_target___idf_efuse
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32/esp_efuse_table.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -Wno-format
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/spi_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/platform_port/include
  OBJECT_DIR = esp-idf/efuse/CMakeFiles/__idf_efuse.dir
  OBJECT_FILE_DIR = esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32

build esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32/esp_efuse_fields.c.obj: C_COMPILER____idf_efuse_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/esp32/esp_efuse_fields.c || cmake_object_order_depends_target___idf_efuse
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32/esp_efuse_fields.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -Wno-format
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/spi_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/platform_port/include
  OBJECT_DIR = esp-idf/efuse/CMakeFiles/__idf_efuse.dir
  OBJECT_FILE_DIR = esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32

build esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32/esp_efuse_utility.c.obj: C_COMPILER____idf_efuse_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/esp32/esp_efuse_utility.c || cmake_object_order_depends_target___idf_efuse
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32/esp_efuse_utility.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -Wno-format
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/spi_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/platform_port/include
  OBJECT_DIR = esp-idf/efuse/CMakeFiles/__idf_efuse.dir
  OBJECT_FILE_DIR = esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32

build esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_api.c.obj: C_COMPILER____idf_efuse_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/src/esp_efuse_api.c || cmake_object_order_depends_target___idf_efuse
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_api.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -Wno-format
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/spi_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/platform_port/include
  OBJECT_DIR = esp-idf/efuse/CMakeFiles/__idf_efuse.dir
  OBJECT_FILE_DIR = esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src

build esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_fields.c.obj: C_COMPILER____idf_efuse_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/src/esp_efuse_fields.c || cmake_object_order_depends_target___idf_efuse
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_fields.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -Wno-format
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/spi_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/platform_port/include
  OBJECT_DIR = esp-idf/efuse/CMakeFiles/__idf_efuse.dir
  OBJECT_FILE_DIR = esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src

build esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_utility.c.obj: C_COMPILER____idf_efuse_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/src/esp_efuse_utility.c || cmake_object_order_depends_target___idf_efuse
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_utility.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -Wno-format
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/spi_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/platform_port/include
  OBJECT_DIR = esp-idf/efuse/CMakeFiles/__idf_efuse.dir
  OBJECT_FILE_DIR = esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src

build esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/efuse_controller/keys/without_key_purposes/three_key_blocks/esp_efuse_api_key.c.obj: C_COMPILER____idf_efuse_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/src/efuse_controller/keys/without_key_purposes/three_key_blocks/esp_efuse_api_key.c || cmake_object_order_depends_target___idf_efuse
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/efuse_controller/keys/without_key_purposes/three_key_blocks/esp_efuse_api_key.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -Wno-format
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/spi_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/platform_port/include
  OBJECT_DIR = esp-idf/efuse/CMakeFiles/__idf_efuse.dir
  OBJECT_FILE_DIR = esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/efuse_controller/keys/without_key_purposes/three_key_blocks


# =============================================================================
# Link build statements for STATIC_LIBRARY target __idf_efuse


#############################################
# Link the static library esp-idf/efuse/libefuse.a

build esp-idf/efuse/libefuse.a: C_STATIC_LIBRARY_LINKER____idf_efuse_ esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32/esp_efuse_table.c.obj esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32/esp_efuse_fields.c.obj esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32/esp_efuse_utility.c.obj esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_api.c.obj esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_fields.c.obj esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_utility.c.obj esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/efuse_controller/keys/without_key_purposes/three_key_blocks/esp_efuse_api_key.c.obj || esp-idf/esp_system/libesp_system.a
  LANGUAGE_COMPILE_FLAGS = -mlongcalls -Wno-frame-address
  OBJECT_DIR = esp-idf/efuse/CMakeFiles/__idf_efuse.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = esp-idf/efuse/libefuse.a
  TARGET_PDB = efuse.a.dbg


#############################################
# Utility command for efuse-common-table

build esp-idf/efuse/efuse-common-table: phony esp-idf/efuse/CMakeFiles/efuse-common-table


#############################################
# Utility command for efuse_common_table

build esp-idf/efuse/efuse_common_table: phony esp-idf/efuse/CMakeFiles/efuse_common_table esp-idf/efuse/efuse-common-table


#############################################
# Utility command for efuse-custom-table

build esp-idf/efuse/efuse-custom-table: phony


#############################################
# Utility command for efuse_custom_table

build esp-idf/efuse/efuse_custom_table: phony esp-idf/efuse/CMakeFiles/efuse_custom_table esp-idf/efuse/efuse-custom-table


#############################################
# Utility command for show-efuse-table

build esp-idf/efuse/show-efuse-table: phony esp-idf/efuse/CMakeFiles/show-efuse-table


#############################################
# Utility command for show_efuse_table

build esp-idf/efuse/show_efuse_table: phony esp-idf/efuse/CMakeFiles/show_efuse_table esp-idf/efuse/show-efuse-table


#############################################
# Utility command for efuse_test_table

build esp-idf/efuse/efuse_test_table: phony esp-idf/efuse/CMakeFiles/efuse_test_table


#############################################
# Utility command for edit_cache

build esp-idf/efuse/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/efuse && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/bin/ccmake -S/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject -B/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build esp-idf/efuse/edit_cache: phony esp-idf/efuse/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build esp-idf/efuse/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/efuse && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/bin/cmake --regenerate-during-build -S/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject -B/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build esp-idf/efuse/rebuild_cache: phony esp-idf/efuse/CMakeFiles/rebuild_cache.util


#############################################
# Custom command for esp-idf/efuse/CMakeFiles/efuse-common-table

build esp-idf/efuse/CMakeFiles/efuse-common-table | ${cmake_ninja_workdir}esp-idf/efuse/CMakeFiles/efuse-common-table: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/efuse && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/python_env/idf5.0_py3.10_env/bin/python /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/efuse_table_gen.py /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/esp32/esp_efuse_table.csv -t esp32 --max_blk_len 192


#############################################
# Custom command for esp-idf/efuse/CMakeFiles/efuse_common_table

build esp-idf/efuse/CMakeFiles/efuse_common_table | ${cmake_ninja_workdir}esp-idf/efuse/CMakeFiles/efuse_common_table: CUSTOM_COMMAND || esp-idf/efuse/efuse-common-table
  COMMAND = cd /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/efuse && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/bin/cmake -E echo
  DESC = Warning: command "efuse_common_table" is deprecated. Have you wanted to run "efuse-common-table" instead?


#############################################
# Custom command for esp-idf/efuse/CMakeFiles/efuse_custom_table

build esp-idf/efuse/CMakeFiles/efuse_custom_table | ${cmake_ninja_workdir}esp-idf/efuse/CMakeFiles/efuse_custom_table: CUSTOM_COMMAND || esp-idf/efuse/efuse-custom-table
  COMMAND = cd /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/efuse && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/bin/cmake -E echo
  DESC = Warning: command "efuse_custom_table" is deprecated. Have you wanted to run "efuse-custom-table" instead?


#############################################
# Custom command for esp-idf/efuse/CMakeFiles/show-efuse-table

build esp-idf/efuse/CMakeFiles/show-efuse-table | ${cmake_ninja_workdir}esp-idf/efuse/CMakeFiles/show-efuse-table: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/efuse && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/python_env/idf5.0_py3.10_env/bin/python /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/efuse_table_gen.py /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/esp32/esp_efuse_table.csv -t esp32 --max_blk_len 192 --info


#############################################
# Custom command for esp-idf/efuse/CMakeFiles/show_efuse_table

build esp-idf/efuse/CMakeFiles/show_efuse_table | ${cmake_ninja_workdir}esp-idf/efuse/CMakeFiles/show_efuse_table: CUSTOM_COMMAND || esp-idf/efuse/show-efuse-table
  COMMAND = cd /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/efuse && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/bin/cmake -E echo
  DESC = Warning: command "show_efuse_table" is deprecated. Have you wanted to run "show-efuse-table" instead?


#############################################
# Custom command for esp-idf/efuse/CMakeFiles/efuse_test_table

build esp-idf/efuse/CMakeFiles/efuse_test_table | ${cmake_ninja_workdir}esp-idf/efuse/CMakeFiles/efuse_test_table: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/efuse && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/python_env/idf5.0_py3.10_env/bin/python /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/efuse_table_gen.py /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/test/esp_efuse_test_table.csv -t esp32 --max_blk_len 192

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target __idf_esp_system


#############################################
# Order-only phony target for __idf_esp_system

build cmake_object_order_depends_target___idf_esp_system: phony || cmake_object_order_depends_target___idf_esp_hw_support

build esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/esp_err.c.obj: C_COMPILER____idf_esp_system_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_system/esp_err.c || cmake_object_order_depends_target___idf_esp_system
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/esp_err.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -Wno-format
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/spi_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/platform_port/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_app_format/include
  OBJECT_DIR = esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir
  OBJECT_FILE_DIR = esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir


# =============================================================================
# Link build statements for STATIC_LIBRARY target __idf_esp_system


#############################################
# Link the static library esp-idf/esp_system/libesp_system.a

build esp-idf/esp_system/libesp_system.a: C_STATIC_LIBRARY_LINKER____idf_esp_system_ esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/esp_err.c.obj || esp-idf/esp_hw_support/libesp_hw_support.a
  LANGUAGE_COMPILE_FLAGS = -mlongcalls -Wno-frame-address
  OBJECT_DIR = esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = esp-idf/esp_system/libesp_system.a
  TARGET_PDB = esp_system.a.dbg


#############################################
# Utility command for edit_cache

build esp-idf/esp_system/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/esp_system && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/bin/ccmake -S/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject -B/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build esp-idf/esp_system/edit_cache: phony esp-idf/esp_system/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build esp-idf/esp_system/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/esp_system && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/bin/cmake --regenerate-during-build -S/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject -B/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build esp-idf/esp_system/rebuild_cache: phony esp-idf/esp_system/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target __idf_esp_hw_support


#############################################
# Order-only phony target for __idf_esp_hw_support

build cmake_object_order_depends_target___idf_esp_hw_support: phony || cmake_object_order_depends_target___idf_esp_common

build esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/cpu.c.obj: C_COMPILER____idf_esp_hw_support_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/cpu.c || cmake_object_order_depends_target___idf_esp_hw_support
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/cpu.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -Wno-format
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/esp_private -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/spi_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/platform_port/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/private_include
  OBJECT_DIR = esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir
  OBJECT_FILE_DIR = esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir

build esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/esp_memory_utils.c.obj: C_COMPILER____idf_esp_hw_support_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/esp_memory_utils.c || cmake_object_order_depends_target___idf_esp_hw_support
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/esp_memory_utils.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -Wno-format
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/esp_private -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/spi_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/platform_port/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/private_include
  OBJECT_DIR = esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir
  OBJECT_FILE_DIR = esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir

build esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32/rtc_clk.c.obj: C_COMPILER____idf_esp_hw_support_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/rtc_clk.c || cmake_object_order_depends_target___idf_esp_hw_support
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32/rtc_clk.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -Wno-format
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/esp_private -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/spi_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/platform_port/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/private_include
  OBJECT_DIR = esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir
  OBJECT_FILE_DIR = esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32

build esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32/rtc_clk_init.c.obj: C_COMPILER____idf_esp_hw_support_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/rtc_clk_init.c || cmake_object_order_depends_target___idf_esp_hw_support
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32/rtc_clk_init.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -Wno-format
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/esp_private -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/spi_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/platform_port/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/private_include
  OBJECT_DIR = esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir
  OBJECT_FILE_DIR = esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32

build esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32/rtc_init.c.obj: C_COMPILER____idf_esp_hw_support_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/rtc_init.c || cmake_object_order_depends_target___idf_esp_hw_support
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32/rtc_init.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -Wno-format
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/esp_private -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/spi_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/platform_port/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/private_include
  OBJECT_DIR = esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir
  OBJECT_FILE_DIR = esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32

build esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32/rtc_pm.c.obj: C_COMPILER____idf_esp_hw_support_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/rtc_pm.c || cmake_object_order_depends_target___idf_esp_hw_support
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32/rtc_pm.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -Wno-format
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/esp_private -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/spi_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/platform_port/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/private_include
  OBJECT_DIR = esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir
  OBJECT_FILE_DIR = esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32

build esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32/rtc_sleep.c.obj: C_COMPILER____idf_esp_hw_support_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/rtc_sleep.c || cmake_object_order_depends_target___idf_esp_hw_support
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32/rtc_sleep.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -Wno-format
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/esp_private -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/spi_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/platform_port/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/private_include
  OBJECT_DIR = esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir
  OBJECT_FILE_DIR = esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32

build esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32/rtc_time.c.obj: C_COMPILER____idf_esp_hw_support_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/rtc_time.c || cmake_object_order_depends_target___idf_esp_hw_support
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32/rtc_time.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -Wno-format
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/esp_private -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/spi_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/platform_port/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/private_include
  OBJECT_DIR = esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir
  OBJECT_FILE_DIR = esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32

build esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32/chip_info.c.obj: C_COMPILER____idf_esp_hw_support_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/chip_info.c || cmake_object_order_depends_target___idf_esp_hw_support
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32/chip_info.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -Wno-format
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/esp_private -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/spi_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/platform_port/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/private_include
  OBJECT_DIR = esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir
  OBJECT_FILE_DIR = esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32


# =============================================================================
# Link build statements for STATIC_LIBRARY target __idf_esp_hw_support


#############################################
# Link the static library esp-idf/esp_hw_support/libesp_hw_support.a

build esp-idf/esp_hw_support/libesp_hw_support.a: C_STATIC_LIBRARY_LINKER____idf_esp_hw_support_ esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/cpu.c.obj esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/esp_memory_utils.c.obj esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32/rtc_clk.c.obj esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32/rtc_clk_init.c.obj esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32/rtc_init.c.obj esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32/rtc_pm.c.obj esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32/rtc_sleep.c.obj esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32/rtc_time.c.obj esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32/chip_info.c.obj || esp-idf/esp_common/libesp_common.a
  LANGUAGE_COMPILE_FLAGS = -mlongcalls -Wno-frame-address
  OBJECT_DIR = esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = esp-idf/esp_hw_support/libesp_hw_support.a
  TARGET_PDB = esp_hw_support.a.dbg


#############################################
# Utility command for edit_cache

build esp-idf/esp_hw_support/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/esp_hw_support && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/bin/ccmake -S/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject -B/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build esp-idf/esp_hw_support/edit_cache: phony esp-idf/esp_hw_support/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build esp-idf/esp_hw_support/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/esp_hw_support && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/bin/cmake --regenerate-during-build -S/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject -B/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build esp-idf/esp_hw_support/rebuild_cache: phony esp-idf/esp_hw_support/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for edit_cache

build esp-idf/esp_hw_support/port/esp32/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/esp_hw_support/port/esp32 && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/bin/ccmake -S/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject -B/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build esp-idf/esp_hw_support/port/esp32/edit_cache: phony esp-idf/esp_hw_support/port/esp32/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build esp-idf/esp_hw_support/port/esp32/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/esp_hw_support/port/esp32 && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/bin/cmake --regenerate-during-build -S/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject -B/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build esp-idf/esp_hw_support/port/esp32/rebuild_cache: phony esp-idf/esp_hw_support/port/esp32/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target __idf_esp_common


#############################################
# Order-only phony target for __idf_esp_common

build cmake_object_order_depends_target___idf_esp_common: phony || cmake_object_order_depends_target___idf_esp_rom

build esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/src/esp_err_to_name.c.obj: C_COMPILER____idf_esp_common_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/src/esp_err_to_name.c || cmake_object_order_depends_target___idf_esp_common
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/src/esp_err_to_name.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/spi_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/platform_port/include
  OBJECT_DIR = esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir
  OBJECT_FILE_DIR = esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/src


# =============================================================================
# Link build statements for STATIC_LIBRARY target __idf_esp_common


#############################################
# Link the static library esp-idf/esp_common/libesp_common.a

build esp-idf/esp_common/libesp_common.a: C_STATIC_LIBRARY_LINKER____idf_esp_common_ esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/src/esp_err_to_name.c.obj || esp-idf/esp_rom/libesp_rom.a
  LANGUAGE_COMPILE_FLAGS = -mlongcalls -Wno-frame-address
  OBJECT_DIR = esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = esp-idf/esp_common/libesp_common.a
  TARGET_PDB = esp_common.a.dbg


#############################################
# Utility command for edit_cache

build esp-idf/esp_common/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/esp_common && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/bin/ccmake -S/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject -B/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build esp-idf/esp_common/edit_cache: phony esp-idf/esp_common/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build esp-idf/esp_common/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/esp_common && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/bin/cmake --regenerate-during-build -S/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject -B/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build esp-idf/esp_common/rebuild_cache: phony esp-idf/esp_common/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target __idf_esp_rom


#############################################
# Order-only phony target for __idf_esp_rom

build cmake_object_order_depends_target___idf_esp_rom: phony || cmake_object_order_depends_target___idf_log

build esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_crc.c.obj: C_COMPILER____idf_esp_rom_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/patches/esp_rom_crc.c || cmake_object_order_depends_target___idf_esp_rom
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_crc.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/platform_port/include
  OBJECT_DIR = esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir
  OBJECT_FILE_DIR = esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches

build esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_sys.c.obj: C_COMPILER____idf_esp_rom_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/patches/esp_rom_sys.c || cmake_object_order_depends_target___idf_esp_rom
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_sys.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/platform_port/include
  OBJECT_DIR = esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir
  OBJECT_FILE_DIR = esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches

build esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_uart.c.obj: C_COMPILER____idf_esp_rom_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/patches/esp_rom_uart.c || cmake_object_order_depends_target___idf_esp_rom
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_uart.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/platform_port/include
  OBJECT_DIR = esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir
  OBJECT_FILE_DIR = esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches

build esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_spiflash.c.obj: C_COMPILER____idf_esp_rom_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/patches/esp_rom_spiflash.c || cmake_object_order_depends_target___idf_esp_rom
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_spiflash.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/platform_port/include
  OBJECT_DIR = esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir
  OBJECT_FILE_DIR = esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches

build esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_regi2c.c.obj: C_COMPILER____idf_esp_rom_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/patches/esp_rom_regi2c.c || cmake_object_order_depends_target___idf_esp_rom
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_regi2c.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/platform_port/include
  OBJECT_DIR = esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir
  OBJECT_FILE_DIR = esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches

build esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_efuse.c.obj: C_COMPILER____idf_esp_rom_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/patches/esp_rom_efuse.c || cmake_object_order_depends_target___idf_esp_rom
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_efuse.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/platform_port/include
  OBJECT_DIR = esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir
  OBJECT_FILE_DIR = esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches

build esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_longjmp.S.obj: ASM_COMPILER____idf_esp_rom_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/patches/esp_rom_longjmp.S || cmake_object_order_depends_target___idf_esp_rom
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_longjmp.S.obj.d
  FLAGS = -mlongcalls  -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/platform_port/include
  OBJECT_DIR = esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir
  OBJECT_FILE_DIR = esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches


# =============================================================================
# Link build statements for STATIC_LIBRARY target __idf_esp_rom


#############################################
# Link the static library esp-idf/esp_rom/libesp_rom.a

build esp-idf/esp_rom/libesp_rom.a: C_STATIC_LIBRARY_LINKER____idf_esp_rom_ esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_crc.c.obj esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_sys.c.obj esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_uart.c.obj esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_spiflash.c.obj esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_regi2c.c.obj esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_efuse.c.obj esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_longjmp.S.obj || esp-idf/log/liblog.a
  LANGUAGE_COMPILE_FLAGS = -mlongcalls -Wno-frame-address
  OBJECT_DIR = esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = esp-idf/esp_rom/libesp_rom.a
  TARGET_PDB = esp_rom.a.dbg


#############################################
# Utility command for edit_cache

build esp-idf/esp_rom/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/esp_rom && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/bin/ccmake -S/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject -B/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build esp-idf/esp_rom/edit_cache: phony esp-idf/esp_rom/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build esp-idf/esp_rom/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/esp_rom && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/bin/cmake --regenerate-during-build -S/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject -B/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build esp-idf/esp_rom/rebuild_cache: phony esp-idf/esp_rom/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target __idf_log


#############################################
# Order-only phony target for __idf_log

build cmake_object_order_depends_target___idf_log: phony || esp-idf/log/CMakeFiles/__idf_log.dir

build esp-idf/log/CMakeFiles/__idf_log.dir/log.c.obj: C_COMPILER____idf_log_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/log.c || cmake_object_order_depends_target___idf_log
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/log/CMakeFiles/__idf_log.dir/log.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/platform_port/include
  OBJECT_DIR = esp-idf/log/CMakeFiles/__idf_log.dir
  OBJECT_FILE_DIR = esp-idf/log/CMakeFiles/__idf_log.dir

build esp-idf/log/CMakeFiles/__idf_log.dir/log_buffers.c.obj: C_COMPILER____idf_log_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/log_buffers.c || cmake_object_order_depends_target___idf_log
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/log/CMakeFiles/__idf_log.dir/log_buffers.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/platform_port/include
  OBJECT_DIR = esp-idf/log/CMakeFiles/__idf_log.dir
  OBJECT_FILE_DIR = esp-idf/log/CMakeFiles/__idf_log.dir

build esp-idf/log/CMakeFiles/__idf_log.dir/log_noos.c.obj: C_COMPILER____idf_log_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/log_noos.c || cmake_object_order_depends_target___idf_log
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/log/CMakeFiles/__idf_log.dir/log_noos.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/platform_port/include
  OBJECT_DIR = esp-idf/log/CMakeFiles/__idf_log.dir
  OBJECT_FILE_DIR = esp-idf/log/CMakeFiles/__idf_log.dir


# =============================================================================
# Link build statements for STATIC_LIBRARY target __idf_log


#############################################
# Link the static library esp-idf/log/liblog.a

build esp-idf/log/liblog.a: C_STATIC_LIBRARY_LINKER____idf_log_ esp-idf/log/CMakeFiles/__idf_log.dir/log.c.obj esp-idf/log/CMakeFiles/__idf_log.dir/log_buffers.c.obj esp-idf/log/CMakeFiles/__idf_log.dir/log_noos.c.obj
  LANGUAGE_COMPILE_FLAGS = -mlongcalls -Wno-frame-address
  OBJECT_DIR = esp-idf/log/CMakeFiles/__idf_log.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = esp-idf/log/liblog.a
  TARGET_PDB = log.a.dbg


#############################################
# Utility command for edit_cache

build esp-idf/log/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/log && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/bin/ccmake -S/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject -B/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build esp-idf/log/edit_cache: phony esp-idf/log/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build esp-idf/log/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/log && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/bin/cmake --regenerate-during-build -S/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject -B/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build esp-idf/log/rebuild_cache: phony esp-idf/log/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for bootloader_check_size

build esp-idf/esptool_py/bootloader_check_size: phony esp-idf/esptool_py/CMakeFiles/bootloader_check_size gen_project_binary


#############################################
# Utility command for edit_cache

build esp-idf/esptool_py/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/esptool_py && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/bin/ccmake -S/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject -B/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build esp-idf/esptool_py/edit_cache: phony esp-idf/esptool_py/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build esp-idf/esptool_py/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/esptool_py && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/bin/cmake --regenerate-during-build -S/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject -B/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build esp-idf/esptool_py/rebuild_cache: phony esp-idf/esptool_py/CMakeFiles/rebuild_cache.util


#############################################
# Custom command for esp-idf/esptool_py/CMakeFiles/bootloader_check_size

build esp-idf/esptool_py/CMakeFiles/bootloader_check_size | ${cmake_ninja_workdir}esp-idf/esptool_py/CMakeFiles/bootloader_check_size: CUSTOM_COMMAND || _project_elf_src bootloader.elf esp-idf/bootloader_support/libbootloader_support.a esp-idf/efuse/libefuse.a esp-idf/esp_app_format/libesp_app_format.a esp-idf/esp_common/libesp_common.a esp-idf/esp_hw_support/libesp_hw_support.a esp-idf/esp_rom/libesp_rom.a esp-idf/esp_system/libesp_system.a esp-idf/hal/libhal.a esp-idf/log/liblog.a esp-idf/main/libmain.a esp-idf/micro-ecc/libmicro-ecc.a esp-idf/soc/libsoc.a esp-idf/xtensa/libxtensa.a gen_project_binary
  COMMAND = cd /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/esptool_py && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/python_env/idf5.0_py3.10_env/bin/python /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/partition_table/check_sizes.py --offset 0x8000 bootloader 0x1000 /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/bootloader.bin

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for edit_cache

build esp-idf/partition_table/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/partition_table && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/bin/ccmake -S/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject -B/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build esp-idf/partition_table/edit_cache: phony esp-idf/partition_table/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build esp-idf/partition_table/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/partition_table && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/bin/cmake --regenerate-during-build -S/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject -B/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build esp-idf/partition_table/rebuild_cache: phony esp-idf/partition_table/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for edit_cache

build esp-idf/bootloader/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/bootloader && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/bin/ccmake -S/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject -B/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build esp-idf/bootloader/edit_cache: phony esp-idf/bootloader/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build esp-idf/bootloader/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/bootloader && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/bin/cmake --regenerate-during-build -S/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject -B/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build esp-idf/bootloader/rebuild_cache: phony esp-idf/bootloader/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for edit_cache

build esp-idf/freertos/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/freertos && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/bin/ccmake -S/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject -B/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build esp-idf/freertos/edit_cache: phony esp-idf/freertos/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build esp-idf/freertos/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/freertos && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/bin/cmake --regenerate-during-build -S/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject -B/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build esp-idf/freertos/rebuild_cache: phony esp-idf/freertos/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target __idf_main


#############################################
# Order-only phony target for __idf_main

build cmake_object_order_depends_target___idf_main: phony || cmake_object_order_depends_target___idf_xtensa

build esp-idf/main/CMakeFiles/__idf_main.dir/bootloader_start.c.obj: C_COMPILER____idf_main_ /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject/main/bootloader_start.c || cmake_object_order_depends_target___idf_main
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = esp-idf/main/CMakeFiles/__idf_main.dir/bootloader_start.c.obj.d
  FLAGS = -mlongcalls -Wno-frame-address  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject=. -fmacro-prefix-map=/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4=/IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\"v5.0.4-dirty\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1
  INCLUDES = -I/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/config -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/include/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/include/soc/esp32 -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/private_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/platform_include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/. -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/bootloader_flash/include -I/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/private_include
  OBJECT_DIR = esp-idf/main/CMakeFiles/__idf_main.dir
  OBJECT_FILE_DIR = esp-idf/main/CMakeFiles/__idf_main.dir


# =============================================================================
# Link build statements for STATIC_LIBRARY target __idf_main


#############################################
# Link the static library esp-idf/main/libmain.a

build esp-idf/main/libmain.a: C_STATIC_LIBRARY_LINKER____idf_main_ esp-idf/main/CMakeFiles/__idf_main.dir/bootloader_start.c.obj || esp-idf/xtensa/libxtensa.a
  LANGUAGE_COMPILE_FLAGS = -mlongcalls -Wno-frame-address
  OBJECT_DIR = esp-idf/main/CMakeFiles/__idf_main.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = esp-idf/main/libmain.a
  TARGET_PDB = main.a.dbg


#############################################
# Utility command for edit_cache

build esp-idf/main/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/main && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/bin/ccmake -S/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject -B/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build esp-idf/main/edit_cache: phony esp-idf/main/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build esp-idf/main/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/main && /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/bin/cmake --regenerate-during-build -S/Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject -B/Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build esp-idf/main/rebuild_cache: phony esp-idf/main/CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

build __idf_bootloader_support: phony esp-idf/bootloader_support/libbootloader_support.a

build __idf_efuse: phony esp-idf/efuse/libefuse.a

build __idf_esp_app_format: phony esp-idf/esp_app_format/libesp_app_format.a

build __idf_esp_common: phony esp-idf/esp_common/libesp_common.a

build __idf_esp_hw_support: phony esp-idf/esp_hw_support/libesp_hw_support.a

build __idf_esp_rom: phony esp-idf/esp_rom/libesp_rom.a

build __idf_esp_system: phony esp-idf/esp_system/libesp_system.a

build __idf_hal: phony esp-idf/hal/libhal.a

build __idf_log: phony esp-idf/log/liblog.a

build __idf_main: phony esp-idf/main/libmain.a

build __idf_micro-ecc: phony esp-idf/micro-ecc/libmicro-ecc.a

build __idf_soc: phony esp-idf/soc/libsoc.a

build __idf_xtensa: phony esp-idf/xtensa/libxtensa.a

build bootloader_check_size: phony esp-idf/esptool_py/bootloader_check_size

build efuse-common-table: phony esp-idf/efuse/efuse-common-table

build efuse-custom-table: phony esp-idf/efuse/efuse-custom-table

build efuse_common_table: phony esp-idf/efuse/efuse_common_table

build efuse_custom_table: phony esp-idf/efuse/efuse_custom_table

build efuse_test_table: phony esp-idf/efuse/efuse_test_table

build libbootloader_support.a: phony esp-idf/bootloader_support/libbootloader_support.a

build libefuse.a: phony esp-idf/efuse/libefuse.a

build libesp_app_format.a: phony esp-idf/esp_app_format/libesp_app_format.a

build libesp_common.a: phony esp-idf/esp_common/libesp_common.a

build libesp_hw_support.a: phony esp-idf/esp_hw_support/libesp_hw_support.a

build libesp_rom.a: phony esp-idf/esp_rom/libesp_rom.a

build libesp_system.a: phony esp-idf/esp_system/libesp_system.a

build libhal.a: phony esp-idf/hal/libhal.a

build liblog.a: phony esp-idf/log/liblog.a

build libmain.a: phony esp-idf/main/libmain.a

build libmicro-ecc.a: phony esp-idf/micro-ecc/libmicro-ecc.a

build libsoc.a: phony esp-idf/soc/libsoc.a

build libxtensa.a: phony esp-idf/xtensa/libxtensa.a

build show-efuse-table: phony esp-idf/efuse/show-efuse-table

build show_efuse_table: phony esp-idf/efuse/show_efuse_table

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader

build all: phony app bootloader.elf esp-idf/all

# =============================================================================

#############################################
# Folder: /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf

build esp-idf/all: phony esp-idf/xtensa/all esp-idf/newlib/all esp-idf/soc/all esp-idf/micro-ecc/all esp-idf/hal/all esp-idf/spi_flash/all esp-idf/esp_app_format/all esp-idf/bootloader_support/all esp-idf/efuse/all esp-idf/esp_system/all esp-idf/esp_hw_support/all esp-idf/esp_common/all esp-idf/esp_rom/all esp-idf/log/all esp-idf/esptool_py/all esp-idf/partition_table/all esp-idf/bootloader/all esp-idf/freertos/all esp-idf/main/all

# =============================================================================

#############################################
# Folder: /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/bootloader

build esp-idf/bootloader/all: phony

# =============================================================================

#############################################
# Folder: /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/bootloader_support

build esp-idf/bootloader_support/all: phony esp-idf/bootloader_support/libbootloader_support.a

# =============================================================================

#############################################
# Folder: /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/efuse

build esp-idf/efuse/all: phony esp-idf/efuse/libefuse.a

# =============================================================================

#############################################
# Folder: /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/esp_app_format

build esp-idf/esp_app_format/all: phony esp-idf/esp_app_format/libesp_app_format.a

# =============================================================================

#############################################
# Folder: /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/esp_common

build esp-idf/esp_common/all: phony esp-idf/esp_common/libesp_common.a

# =============================================================================

#############################################
# Folder: /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/esp_hw_support

build esp-idf/esp_hw_support/all: phony esp-idf/esp_hw_support/libesp_hw_support.a esp-idf/esp_hw_support/port/esp32/all

# =============================================================================

#############################################
# Folder: /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/esp_hw_support/port/esp32

build esp-idf/esp_hw_support/port/esp32/all: phony

# =============================================================================

#############################################
# Folder: /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/esp_rom

build esp-idf/esp_rom/all: phony esp-idf/esp_rom/libesp_rom.a

# =============================================================================

#############################################
# Folder: /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/esp_system

build esp-idf/esp_system/all: phony esp-idf/esp_system/libesp_system.a

# =============================================================================

#############################################
# Folder: /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/esptool_py

build esp-idf/esptool_py/all: phony

# =============================================================================

#############################################
# Folder: /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/freertos

build esp-idf/freertos/all: phony

# =============================================================================

#############################################
# Folder: /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/hal

build esp-idf/hal/all: phony esp-idf/hal/libhal.a

# =============================================================================

#############################################
# Folder: /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/log

build esp-idf/log/all: phony esp-idf/log/liblog.a

# =============================================================================

#############################################
# Folder: /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/main

build esp-idf/main/all: phony esp-idf/main/libmain.a

# =============================================================================

#############################################
# Folder: /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/micro-ecc

build esp-idf/micro-ecc/all: phony esp-idf/micro-ecc/libmicro-ecc.a

# =============================================================================

#############################################
# Folder: /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/newlib

build esp-idf/newlib/all: phony

# =============================================================================

#############################################
# Folder: /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/partition_table

build esp-idf/partition_table/all: phony

# =============================================================================

#############################################
# Folder: /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/soc

build esp-idf/soc/all: phony esp-idf/soc/libsoc.a esp-idf/soc/esp32/all

# =============================================================================

#############################################
# Folder: /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/soc/esp32

build esp-idf/soc/esp32/all: phony

# =============================================================================

#############################################
# Folder: /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/spi_flash

build esp-idf/spi_flash/all: phony

# =============================================================================

#############################################
# Folder: /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/build/bootloader/esp-idf/xtensa

build esp-idf/xtensa/all: phony esp-idf/xtensa/libxtensa.a

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/.git/HEAD /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/.git/modules/components/bootloader/subproject/components/micro-ecc/micro-ecc/HEAD /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/.git/modules/components/bt/controller/lib_esp32/HEAD /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/.git/modules/components/bt/controller/lib_esp32c2/esp32c2-bt-lib/HEAD /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/.git/modules/components/bt/controller/lib_esp32c3_family/HEAD /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/.git/modules/components/bt/controller/lib_esp32h2/esp32h2-bt-lib/HEAD /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/.git/modules/components/bt/host/nimble/nimble/HEAD /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/.git/modules/components/cmock/CMock/HEAD /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/.git/modules/components/esp_phy/lib/HEAD /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/.git/modules/components/esp_wifi/lib/HEAD /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/.git/modules/components/heap/tlsf/HEAD /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/.git/modules/components/ieee802154/lib/HEAD /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/.git/modules/components/json/cJSON/HEAD /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/.git/modules/components/lwip/lwip/HEAD /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/.git/modules/components/mbedtls/mbedtls/HEAD /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/.git/modules/components/mqtt/esp-mqtt/HEAD /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/.git/modules/components/openthread/lib/HEAD /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/.git/modules/components/openthread/openthread/HEAD /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/.git/modules/components/protobuf-c/protobuf-c/HEAD /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/.git/modules/components/spiffs/spiffs/HEAD /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/.git/modules/components/unity/unity/HEAD /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/CMakeLists.txt /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/CMakeLists.txt /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/project_include.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject/CMakeLists.txt /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject/components/micro-ecc/CMakeLists.txt /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject/components/micro-ecc/micro-ecc/.git /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject/main/CMakeLists.txt /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/CMakeLists.txt /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bt/controller/lib_esp32/.git /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bt/controller/lib_esp32c2/esp32c2-bt-lib/.git /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bt/controller/lib_esp32c3_family/.git /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bt/controller/lib_esp32h2/esp32h2-bt-lib/.git /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bt/host/nimble/nimble/.git /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/cmock/CMock/.git /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/CMakeLists.txt /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/esp32/sources.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_app_format/CMakeLists.txt /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/CMakeLists.txt /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/project_include.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/CMakeLists.txt /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/CMakeLists.txt /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_phy/lib/.git /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/CMakeLists.txt /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_system/CMakeLists.txt /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_wifi/lib/.git /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esptool_py/CMakeLists.txt /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esptool_py/project_include.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/freertos/CMakeLists.txt /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/CMakeLists.txt /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/heap/tlsf/.git /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/ieee802154/lib/.git /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/json/cJSON/.git /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/CMakeLists.txt /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/lwip/lwip/.git /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/mbedtls/mbedtls/.git /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/mqtt/esp-mqtt/.git /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/CMakeLists.txt /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/openthread/lib/.git /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/openthread/openthread/.git /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/partition_table/CMakeLists.txt /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/partition_table/project_include.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/protobuf-c/protobuf-c/.git /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/CMakeLists.txt /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/CMakeLists.txt /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/spi_flash/CMakeLists.txt /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/spiffs/spiffs/.git /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/unity/unity/.git /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/CMakeLists.txt /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/project_include.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/tools/cmake/build.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/tools/cmake/component.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/tools/cmake/depgraph.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/tools/cmake/dfu.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/tools/cmake/git_submodules.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/tools/cmake/idf.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/tools/cmake/kconfig.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/tools/cmake/ldgen.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/tools/cmake/project.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/tools/cmake/project_description.json.in /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/tools/cmake/targets.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/tools/cmake/third_party/GetGitRevisionDescription.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/tools/cmake/third_party/GetGitRevisionDescription.cmake.in /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/tools/cmake/tool_version_check.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/tools/cmake/toolchain-esp32.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/tools/cmake/uf2.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/tools/cmake/utilities.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/tools/cmake/version.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/tools/kconfig_new/confgen.py /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/tools/kconfig_new/config.env.in /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/share/cmake-3.24/Modules/CMakeASMInformation.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/share/cmake-3.24/Modules/CMakeCInformation.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/share/cmake-3.24/Modules/CMakeCXXInformation.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/share/cmake-3.24/Modules/CMakeCheckCompilerFlagCommonPatterns.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/share/cmake-3.24/Modules/CMakeCommonLanguageInclude.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/share/cmake-3.24/Modules/CMakeGenericSystem.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/share/cmake-3.24/Modules/CMakeInitializeConfigs.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/share/cmake-3.24/Modules/CMakeLanguageInformation.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/share/cmake-3.24/Modules/CMakeSystemSpecificInformation.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/share/cmake-3.24/Modules/CMakeSystemSpecificInitialize.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/share/cmake-3.24/Modules/CheckCCompilerFlag.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/share/cmake-3.24/Modules/CheckCSourceCompiles.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/share/cmake-3.24/Modules/CheckCXXCompilerFlag.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/share/cmake-3.24/Modules/CheckCXXSourceCompiles.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/share/cmake-3.24/Modules/CheckIncludeFile.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/share/cmake-3.24/Modules/CheckIncludeFileCXX.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/share/cmake-3.24/Modules/CheckTypeSize.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/share/cmake-3.24/Modules/Compiler/CMakeCommonCompilerMacros.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/share/cmake-3.24/Modules/Compiler/GNU-ASM.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/share/cmake-3.24/Modules/Compiler/GNU-C.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/share/cmake-3.24/Modules/Compiler/GNU-CXX.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/share/cmake-3.24/Modules/Compiler/GNU.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/share/cmake-3.24/Modules/ExternalProject.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/share/cmake-3.24/Modules/FindGit.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/share/cmake-3.24/Modules/FindPackageHandleStandardArgs.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/share/cmake-3.24/Modules/FindPackageMessage.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/share/cmake-3.24/Modules/Internal/CheckCompilerFlag.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/share/cmake-3.24/Modules/Internal/CheckFlagCommonConfig.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/share/cmake-3.24/Modules/Internal/CheckSourceCompiles.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/share/cmake-3.24/Modules/Platform/Generic.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/sdkconfig CMakeCache.txt CMakeFiles/3.24.0/CMakeASMCompiler.cmake CMakeFiles/3.24.0/CMakeCCompiler.cmake CMakeFiles/3.24.0/CMakeCXXCompiler.cmake CMakeFiles/3.24.0/CMakeSystem.cmake CMakeFiles/git-data/grabRef.cmake config/sdkconfig.cmake config/sdkconfig.h
  pool = console


#############################################
# A missing CMake input file is not an error.

build /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/.git/HEAD /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/.git/modules/components/bootloader/subproject/components/micro-ecc/micro-ecc/HEAD /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/.git/modules/components/bt/controller/lib_esp32/HEAD /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/.git/modules/components/bt/controller/lib_esp32c2/esp32c2-bt-lib/HEAD /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/.git/modules/components/bt/controller/lib_esp32c3_family/HEAD /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/.git/modules/components/bt/controller/lib_esp32h2/esp32h2-bt-lib/HEAD /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/.git/modules/components/bt/host/nimble/nimble/HEAD /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/.git/modules/components/cmock/CMock/HEAD /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/.git/modules/components/esp_phy/lib/HEAD /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/.git/modules/components/esp_wifi/lib/HEAD /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/.git/modules/components/heap/tlsf/HEAD /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/.git/modules/components/ieee802154/lib/HEAD /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/.git/modules/components/json/cJSON/HEAD /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/.git/modules/components/lwip/lwip/HEAD /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/.git/modules/components/mbedtls/mbedtls/HEAD /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/.git/modules/components/mqtt/esp-mqtt/HEAD /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/.git/modules/components/openthread/lib/HEAD /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/.git/modules/components/openthread/openthread/HEAD /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/.git/modules/components/protobuf-c/protobuf-c/HEAD /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/.git/modules/components/spiffs/spiffs/HEAD /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/.git/modules/components/unity/unity/HEAD /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/CMakeLists.txt /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/CMakeLists.txt /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/project_include.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject/CMakeLists.txt /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject/components/micro-ecc/CMakeLists.txt /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject/components/micro-ecc/micro-ecc/.git /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader/subproject/main/CMakeLists.txt /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bootloader_support/CMakeLists.txt /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bt/controller/lib_esp32/.git /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bt/controller/lib_esp32c2/esp32c2-bt-lib/.git /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bt/controller/lib_esp32c3_family/.git /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bt/controller/lib_esp32h2/esp32h2-bt-lib/.git /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/bt/host/nimble/nimble/.git /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/cmock/CMock/.git /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/CMakeLists.txt /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/efuse/esp32/sources.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_app_format/CMakeLists.txt /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/CMakeLists.txt /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_common/project_include.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/CMakeLists.txt /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_hw_support/port/esp32/CMakeLists.txt /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_phy/lib/.git /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_rom/CMakeLists.txt /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_system/CMakeLists.txt /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esp_wifi/lib/.git /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esptool_py/CMakeLists.txt /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/esptool_py/project_include.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/freertos/CMakeLists.txt /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/hal/CMakeLists.txt /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/heap/tlsf/.git /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/ieee802154/lib/.git /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/json/cJSON/.git /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/log/CMakeLists.txt /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/lwip/lwip/.git /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/mbedtls/mbedtls/.git /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/mqtt/esp-mqtt/.git /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/newlib/CMakeLists.txt /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/openthread/lib/.git /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/openthread/openthread/.git /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/partition_table/CMakeLists.txt /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/partition_table/project_include.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/protobuf-c/protobuf-c/.git /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/CMakeLists.txt /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/soc/esp32/CMakeLists.txt /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/spi_flash/CMakeLists.txt /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/spiffs/spiffs/.git /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/unity/unity/.git /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/CMakeLists.txt /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/components/xtensa/project_include.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/tools/cmake/build.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/tools/cmake/component.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/tools/cmake/depgraph.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/tools/cmake/dfu.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/tools/cmake/git_submodules.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/tools/cmake/idf.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/tools/cmake/kconfig.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/tools/cmake/ldgen.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/tools/cmake/project.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/tools/cmake/project_description.json.in /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/tools/cmake/targets.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/tools/cmake/third_party/GetGitRevisionDescription.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/tools/cmake/third_party/GetGitRevisionDescription.cmake.in /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/tools/cmake/tool_version_check.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/tools/cmake/toolchain-esp32.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/tools/cmake/uf2.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/tools/cmake/utilities.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/tools/cmake/version.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/tools/kconfig_new/confgen.py /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/esp-idf/v5.0.4/tools/kconfig_new/config.env.in /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/share/cmake-3.24/Modules/CMakeASMInformation.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/share/cmake-3.24/Modules/CMakeCInformation.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/share/cmake-3.24/Modules/CMakeCXXInformation.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/share/cmake-3.24/Modules/CMakeCheckCompilerFlagCommonPatterns.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/share/cmake-3.24/Modules/CMakeCommonLanguageInclude.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/share/cmake-3.24/Modules/CMakeGenericSystem.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/share/cmake-3.24/Modules/CMakeInitializeConfigs.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/share/cmake-3.24/Modules/CMakeLanguageInformation.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/share/cmake-3.24/Modules/CMakeSystemSpecificInformation.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/share/cmake-3.24/Modules/CMakeSystemSpecificInitialize.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/share/cmake-3.24/Modules/CheckCCompilerFlag.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/share/cmake-3.24/Modules/CheckCSourceCompiles.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/share/cmake-3.24/Modules/CheckCXXCompilerFlag.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/share/cmake-3.24/Modules/CheckCXXSourceCompiles.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/share/cmake-3.24/Modules/CheckIncludeFile.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/share/cmake-3.24/Modules/CheckIncludeFileCXX.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/share/cmake-3.24/Modules/CheckTypeSize.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/share/cmake-3.24/Modules/Compiler/CMakeCommonCompilerMacros.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/share/cmake-3.24/Modules/Compiler/GNU-ASM.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/share/cmake-3.24/Modules/Compiler/GNU-C.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/share/cmake-3.24/Modules/Compiler/GNU-CXX.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/share/cmake-3.24/Modules/Compiler/GNU.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/share/cmake-3.24/Modules/ExternalProject.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/share/cmake-3.24/Modules/FindGit.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/share/cmake-3.24/Modules/FindPackageHandleStandardArgs.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/share/cmake-3.24/Modules/FindPackageMessage.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/share/cmake-3.24/Modules/Internal/CheckCompilerFlag.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/share/cmake-3.24/Modules/Internal/CheckFlagCommonConfig.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/share/cmake-3.24/Modules/Internal/CheckSourceCompiles.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/.embuild/espressif/tools/cmake/3.24.0/CMake.app/Contents/share/cmake-3.24/Modules/Platform/Generic.cmake /Users/<USER>/code/MyWorks/hardware/hello-world/target/xtensa-esp32-espidf/release/build/esp-idf-sys-709ea49e31f7dcd0/out/sdkconfig CMakeCache.txt CMakeFiles/3.24.0/CMakeASMCompiler.cmake CMakeFiles/3.24.0/CMakeCCompiler.cmake CMakeFiles/3.24.0/CMakeCXXCompiler.cmake CMakeFiles/3.24.0/CMakeSystem.cmake CMakeFiles/git-data/grabRef.cmake config/sdkconfig.cmake config/sdkconfig.h: phony


#############################################
# Clean additional files.

build CMakeFiles/clean.additional: CLEAN_ADDITIONAL


#############################################
# Clean all the built files.

build clean: CLEAN CMakeFiles/clean.additional


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
