{"rustc": 13229274219568542253, "features": "[\"default\", \"logging\", \"prettyplease\", \"runtime\"]", "declared_features": "[\"__cli\", \"__testing_only_extra_assertions\", \"__testing_only_libclang_16\", \"__testing_only_libclang_9\", \"default\", \"experimental\", \"logging\", \"prettyplease\", \"runtime\", \"static\", \"which-rustfmt\"]", "target": 3886691532138051063, "profile": 1369601567987815722, "path": 7518303547391162388, "deps": [[373107762698212489, "proc_macro2", false, 2219716554639435138], [503635761244294217, "regex", false, 15710413286072926052], [950716570147248582, "cexpr", false, 7296080323574235311], [4885725550624711673, "clang_sys", false, 13379659307301327394], [8410525223747752176, "shlex", false, 14580543296397435517], [8519698367355478612, "build_script_build", false, 14050490091410643267], [9423015880379144908, "prettyplease", false, 18226038736018205251], [11903278875415370753, "itertools", false, 8713139106201702824], [12848154260885479101, "bitflags", false, 13549786404876684780], [13066042571740262168, "log", false, 2457453450863095967], [17332570067994900305, "syn", false, 8023740186960312358], [17990358020177143287, "quote", false, 15408376192792080394], [18335655851112826545, "rustc_hash", false, 13500499605672428943]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/bindgen-a3397941847e6bcb/dep-lib-bindgen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}