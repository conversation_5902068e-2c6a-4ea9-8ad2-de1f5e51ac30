{"rustc": 13229274219568542253, "features": "[\"bindgen\", \"cmake\", \"default\", \"dep-cmake\", \"espidf\", \"git\", \"glob\", \"globwalk\", \"home\", \"kconfig\", \"regex\", \"remove_dir_all\", \"serde\", \"serde_json\", \"strum\", \"tempfile\", \"which\"]", "declared_features": "[\"bindgen\", \"cargo_toml\", \"cmake\", \"default\", \"dep-cmake\", \"elf\", \"espidf\", \"git\", \"glob\", \"globwalk\", \"home\", \"kconfig\", \"manifest\", \"pio\", \"regex\", \"remove_dir_all\", \"serde\", \"serde_json\", \"strum\", \"tempfile\", \"toml\", \"ureq\", \"which\", \"xmas-elf\"]", "target": 1630505904053865269, "profile": 1369601567987815722, "path": 3965339768154229032, "deps": [[503635761244294217, "regex", false, 15710413286072926052], [1512951494379724355, "tempfile", false, 15527822781531447162], [4469244843977843975, "remove_dir_all", false, 1635815953637732854], [4544379658388519060, "home", false, 14315034123793711640], [6243494903393190189, "which", false, 16509964937811344890], [6436979938661751600, "serde", false, 15649280168036777094], [7499741813737603141, "dep_cmake", false, 2846215028571564721], [8008191657135824715, "thiserror", false, 6915106491653964717], [8410525223747752176, "shlex", false, 14580543296397435517], [8519698367355478612, "bindgen", false, 2008871171849351906], [10435729446543529114, "bitflags", false, 15850782708886038135], [11207653606310558077, "anyhow", false, 10766973627037259833], [12832915883349295919, "serde_json", false, 947498013349639942], [12988613902613872100, "filetime", false, 8536524350655362713], [13066042571740262168, "log", false, 2457453450863095967], [13572715315612761004, "globwalk", false, 13911867535618617726], [15364576148834874936, "strum", false, 7073163195227680094]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/embuild-c65d5307bdeef971/dep-lib-embuild", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}