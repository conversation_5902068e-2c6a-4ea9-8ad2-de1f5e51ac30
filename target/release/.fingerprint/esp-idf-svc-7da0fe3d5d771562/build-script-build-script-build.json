{"rustc": 13229274219568542253, "features": "[\"alloc\", \"binstart\", \"default\", \"futures-io\", \"std\"]", "declared_features": "[\"alloc\", \"alloc_handler\", \"binstart\", \"critical-section\", \"default\", \"embassy-sync\", \"embassy-time-driver\", \"embassy-time-queue-utils\", \"embedded-storage\", \"experimental\", \"futures-io\", \"libstart\", \"native\", \"nightly\", \"panic_handler\", \"pio\", \"std\", \"wake-from-isr\"]", "target": 5408242616063297496, "profile": 1369601567987815722, "path": 16661581806757162089, "deps": [[4131399637890538147, "embuild", false, 352431617080633158]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/esp-idf-svc-7da0fe3d5d771562/dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}