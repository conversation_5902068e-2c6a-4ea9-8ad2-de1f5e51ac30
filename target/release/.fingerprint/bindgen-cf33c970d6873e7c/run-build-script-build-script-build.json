{"rustc": 13229274219568542253, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[8519698367355478612, "build_script_build", false, 10837900337357825967], [4885725550624711673, "build_script_build", false, 5836406031530614027], [9423015880379144908, "build_script_build", false, 3991043011018266595]], "local": [{"RerunIfEnvChanged": {"var": "LLVM_CONFIG_PATH", "val": null}}, {"RerunIfEnvChanged": {"var": "LIBCLANG_PATH", "val": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib"}}, {"RerunIfEnvChanged": {"var": "LIBCLANG_STATIC_PATH", "val": null}}, {"RerunIfEnvChanged": {"var": "BINDGEN_EXTRA_CLANG_ARGS", "val": null}}, {"RerunIfEnvChanged": {"var": "BINDGEN_EXTRA_CLANG_ARGS_aarch64-apple-darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "BINDGEN_EXTRA_CLANG_ARGS_aarch64_apple_darwin", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}