#!/bin/bash

# 动态 runner 脚本
# 根据环境变量决定使用 QEMU 还是硬件刷机

# 获取二进制文件名，去掉路径和扩展名
BINARY_NAME=$(basename "$1" | sed 's/\.[^.]*$//')

if [ "$ESP32_TARGET" = "qemu" ] || [ "$ESP32_TARGET" = "sim" ]; then
    # QEMU 模拟器模式
    echo "🚀 在 QEMU 模拟器中运行 $BINARY_NAME..."
    exec qemu-system-xtensa-esp32 \
        -nographic \
        -M esp32 \
        -m 4M \
        -kernel "$1"
else
    # 默认硬件刷机模式
    echo "📱 刷写 $BINARY_NAME 到硬件设备..."
    exec espflash flash --monitor "$1"
fi